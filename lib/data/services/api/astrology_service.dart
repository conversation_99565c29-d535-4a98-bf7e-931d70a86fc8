import 'package:flutter/material.dart';
import 'package:sweph/sweph.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;

import '../../../core/constants/ascension_table.dart';
import '../../../core/constants/astrology_constants.dart';
import '../../../core/utils/logger_utils.dart';
import '../../../shared/utils/astrology_calculator.dart';
import '../../../shared/utils/enhanced_timezone_service.dart';
import '../../../shared/utils/julian_date_utils.dart';
import '../../models/astrology/aspect_info.dart';
import '../../models/astrology/chart_calculation_params.dart';
import '../../models/astrology/chart_data.dart';
import '../../models/astrology/chart_settings.dart';
import '../../models/astrology/chart_type.dart';
import '../../models/astrology/planet_position.dart';
import '../../models/interpretation/term_ruler_progression_result.dart';
import '../../models/user/birth_data.dart';

/// 占星服务类
/// 提供行星位置、宮位和相位計算的功能
class AstrologyService {
  bool _initialized = false;
  bool _initializationFailed = false;

  /// 初始化 Swiss Ephemeris
  Future<void> initialize() async {
    if (_initialized || _initializationFailed) {
      return;
    }

    try {
      // 先檢查 SwissEph 是否已經初始化
      try {
        final version = Sweph.swe_version();
        logger.d('Swiss Ephemeris 已經初始化: $version');
        _initialized = true;
        return;
      } catch (checkError) {
        logger.d('需要初始化 Swiss Ephemeris');
      }

      // 嘗試使用默認設置初始化
      await Sweph.init(
        epheAssets: [
          "assets/ephe/seas_18.se1", // For house calc
          "assets/ephe/sefstars.txt", // For star name
          "assets/ephe/seasnam.txt", // For asteriods
          "assets/ephe/sepl_18.se1", // 行星星曆
          "assets/ephe/semo_18.se1", // 月亮星曆
          "assets/ephe/se00433s.se1", // 小行星星曆
        ],
      );
      _initialized = true;
      logger.d('Swiss Ephemeris 初始化成功: ${Sweph.swe_version()}');
    } catch (e) {
      logger.d('Swiss Ephemeris 初始化失敗: $e');
      // 標記初始化失敗，避免重複嘗試
      _initializationFailed = true;
    }
  }

  /// 從設定中獲取宮位系統
  Future<HouseSystem> getHouseSystemFromSettings() async {
    try {
      final settings = await ChartSettings.loadFromPrefs();
      return settings.houseSystem;
    } catch (e) {
      logger.d('從設定中獲取宮位系統時出錯: $e');
      return HouseSystem.placidus; // 默認使用 Placidus 系統
    }
  }

  /// 將宮位系統名稱轉換為 Swiss Ephemeris 的 Hsys 枚舉
  Future<Hsys> getHouseSystem([HouseSystem? systemName]) async {
    // 如果沒有提供系統名稱，從設定中獲取
    final HouseSystem effectiveSystemName =
        systemName ?? await getHouseSystemFromSettings();

    switch (effectiveSystemName) {
      case HouseSystem.placidus:
        return Hsys.P;
      case HouseSystem.koch:
        return Hsys.K;
      case HouseSystem.equal:
        return Hsys.A;
      case HouseSystem.wholeSign:
        return Hsys.W;
      case HouseSystem.campanus:
        return Hsys.C;
      case HouseSystem.regiomontanus:
        return Hsys.R;
      case HouseSystem.topocentric:
        return Hsys.T;
      case HouseSystem.alcabitius:
        return Hsys.B;
      case HouseSystem.porphyry:
        return Hsys.O;
      case HouseSystem.meridianHouses:
        return Hsys.M;
      case HouseSystem.morinus:
        return Hsys.X;
      case HouseSystem.krusinskiPisaGoelzer:
        return Hsys.U;
      case HouseSystem.equalVehlow:
        return Hsys.V;
      case HouseSystem.apcHouses:
        return Hsys.Y;
      default:
        logger.d('不支援的宮位系統: $effectiveSystemName，使用 Placidus 系統');
        return Hsys.P; // 默認使用 Placidus 系統
    }
  }

  /// 計算上升點和宮位
  Future<HouseCuspData?> calculateHouses(
      DateTime birthDateTime, double latitude, double longitude,
      {HouseSystem? houseSystem}) async {
    // 從設定中獲取宮位系統，如果沒有提供
    houseSystem = houseSystem ?? await getHouseSystemFromSettings();

    logger.i(
        '開始計算宮位: ${birthDateTime.toString()}, 緯度: $latitude, 經度: $longitude, 宮位系統: $houseSystem');

    try {
      await initialize();

      // 如果初始化失敗，直接返回默認值
      if (!_initialized || _initializationFailed) {
        logger.w('Swiss Ephemeris 未初始化，使用默認宮位數據');
        return null;
      }

      // 將出生日期時間轉換為儒略日
      final julianDay = await JulianDateUtils.dateTimeToJulianDay(
        birthDateTime,
        latitude,
        longitude,
      );

      try {
        // 獲取宮位系統
        final hsys = await getHouseSystem(houseSystem);
        logger.d('使用宮位系統: $houseSystem');
        HouseCuspData houses = Sweph.swe_houses(
          julianDay,
          latitude,
          longitude,
          hsys,
        );

        logger.d('宮位數據: $houses');
        return houses;
      } catch (e) {
        logger.e('計算宮位時出錯: $e');
        return null;
      }
    } catch (e) {
      logger.e('計算宮位時出錯: $e');
      // 返回默認的等分宮位數據
      return null;
    }
  }

  /// 計算時區偏移量
 static Future<double> calculateZonedDateTimeOffset(double latitude, double longitude,
      int year, int month, int day, int hour, int minute) async {
    // 初始化時區數據，只需要執行一次
    tz.initializeTimeZones();

    String? timezone = await EnhancedTimezoneService()
        .getTimeZoneFromLatLng(latitude, longitude);
    if (timezone != null) {
      logger.d("時區: $timezone");
    } else {
      logger.e("找不到對應時區");
      return 0;
    }

    final tz.Location location = tz.getLocation(timezone);

    // 創建對應的 ZonedDateTime
    final tz.TZDateTime zonedDateTime =
        tz.TZDateTime(location, year, month, day, hour, minute);

    // 獲取時區偏移量（包含夏令時影響）
    final int offsetInSeconds = zonedDateTime.timeZoneOffset.inSeconds;

    // 將秒數轉換為小時
    final double offsetInHours = offsetInSeconds / 3600.0;

    return offsetInHours;
  }

  /// 計算行星位置
  ///
  /// 此方法是計算行星位置的主入口點，它會：
  /// 1. 初始化 Swiss Ephemeris
  /// 2. 計算儒略日
  /// 3. 處理特殊點位（上升點、中天、下降點、天底）
  /// 4. 計算一般行星位置
  /// 5. 處理南交點特殊情況
  Future<List<PlanetPosition>> calculatePlanetPositions(DateTime birthDateTime,
      {double ascendantDegree = 0.0,
      HouseCuspData? housesData,
      required double latitude,
      required double longitude,
      Map<String, bool>? planetVisibility}) async {
    // 如果提供了 housesData，則從中獲取上升點位置
    final double effectiveAscendantDegree =
        housesData != null ? (housesData.cusps[1]) : ascendantDegree;

    try {
      await initialize();

      // 如果初始化失敗，直接返回默認值
      if (!_initialized || _initializationFailed) {
        logger.w('Swiss Ephemeris 未初始化，使用默認行星位置');
        return _getDefaultPlanetPositions(
            effectiveAscendantDegree, housesData, planetVisibility);
      }

      // 將出生日期時間轉換為儒略日
      final julianDay = await JulianDateUtils.dateTimeToJulianDay(
        birthDateTime,
        latitude,
        longitude,
      );

      // 計算行星位置
      final List<PlanetPosition> positions = [];
      PlanetPosition sunPosition;
      bool isDaytime = false;
      // 處理一般行星
      for (final planet in AstrologyConstants.PLANETS) {
        // 檢查行星是否應該顯示
        if (planetVisibility != null &&
            planetVisibility[planet['name'] as String] == false) {
          continue;
        }
        // 跳過特殊點位，因為已經在上面處理過了
        if (['上升', '中天', '下降', '天底'].contains(planet['name'])) {
          continue;
        }
        try {
          // 計算行星位置
          final planetPosition = await _calculatePlanetPosition(planet,
              julianDay, housesData, effectiveAscendantDegree, isDaytime);
          positions.add(planetPosition);
          if (planet['name'] == '太陽') {
            sunPosition = planetPosition;
            isDaytime = _isDaytimeBirth(sunPosition.house);
          }
        } catch (e) {
          if (planet['name'] == '南交點') {
            // 處理南交點特殊情況
            try {
              final southNodePosition = _calculateSouthNodePosition(
                planet,
                positions,
                housesData,
                effectiveAscendantDegree,
              );
              positions.add(southNodePosition);
            } catch (nodeError) {
              logger.e('計算南交點位置時出錯: $nodeError');
            }
          } else {
            logger.e('計算${planet['name']}位置時出錯: $e');
          }
        }
      }

      return positions;
    } catch (e) {
      logger.e('計算行星位置時出錯: $e');
      // 返回默認行星位置
      return _getDefaultPlanetPositions(
          effectiveAscendantDegree, housesData, planetVisibility);
    }
  }

  void _addSpecialPointsIfNeeded({
    required List<PlanetPosition> positions,
    required HouseCuspData? housesData,
    required Map<String, bool>? planetVisibility,
  }) {
    if (housesData != null && housesData.ascmc.isNotEmpty) {
      int? sunHouse;
      for (final pos in positions) {
        if (pos.id == AstrologyConstants.SUN) {
          sunHouse = pos.house;
          break;
        }
      }

      final specialPoints = _calculateSpecialPoints(
        housesData,
        planetVisibility,
        sunHouse: sunHouse,
      );
      positions.addAll(specialPoints);
    }
  }

  void _addSpecialPointsIfNeededDual({
    required List<PlanetPosition> positions,
    required HouseCuspData? housesData,
    required HouseCuspData housesDataDual,
    required Map<String, bool>? planetVisibility,
  }) {
    if (housesData != null && housesData.ascmc.isNotEmpty) {
      int? sunHouse;
      for (final pos in positions) {
        if (pos.id == AstrologyConstants.SUN) {
          sunHouse = pos.house;
          break;
        }
      }

      final specialPoints = _calculateSpecialPointsDual(
        housesData,
        housesDataDual,
        planetVisibility,
        sunHouse: sunHouse,
      );
      positions.addAll(specialPoints);
    }
  }

  /// 計算特殊點位（上升點、中天、下降點、天底）
  List<PlanetPosition> _calculateSpecialPointsDual(HouseCuspData housesData,
      HouseCuspData housesDataDual, Map<String, bool>? planetVisibility,
      {int? sunHouse}) {
    final List<PlanetPosition> specialPoints = [];

    // 如果沒有提供太陽宮位，則嘗試尋找太陽在行星列表中的宮位
    // 這裡只是一個預設值，實際上應該在調用此方法時傳入太陽的宮位

    final asc = AstrologyConstants.PLANETS
        .firstWhere((planet) => planet['id'] == AstrologyConstants.ASCENDANT);
    final mc = AstrologyConstants.PLANETS
        .firstWhere((planet) => planet['id'] == AstrologyConstants.MIDHEAVEN);
    final dsc = AstrologyConstants.PLANETS
        .firstWhere((planet) => planet['id'] == AstrologyConstants.DESCENDANT);
    final ic = AstrologyConstants.PLANETS
        .firstWhere((planet) => planet['id'] == AstrologyConstants.IMUM_COELI);
    // 處理上升點
    if (planetVisibility == null || planetVisibility['上升'] == true) {
      final double ascLongitude = housesData.ascmc[0];
      final String ascSign = _getZodiacSign(ascLongitude);
      final int ascHouse =
          _calculateHouseFromLongitude(ascLongitude, housesDataDual);

      specialPoints.add(_createSpecialPointPosition(
        AstrologyConstants.ASCENDANT,
        asc['name'],
        asc['symbol'],
        asc['color'],
        ascLongitude,
        ascSign,
        ascHouse,
        sunHouse: sunHouse,
      ));
    }

    // 處理中天
    if (planetVisibility == null || planetVisibility['中天'] == true) {
      final double mcLongitude = housesData.ascmc[1];
      final String mcSign = _getZodiacSign(mcLongitude);
      final int mcHouse =
          _calculateHouseFromLongitude(mcLongitude, housesDataDual);

      specialPoints.add(_createSpecialPointPosition(
        AstrologyConstants.MIDHEAVEN,
        mc['name'],
        mc['symbol'],
        mc['color'],
        mcLongitude,
        mcSign,
        mcHouse,
        sunHouse: sunHouse,
      ));
    }

    // 處理下降點
    if (planetVisibility == null || planetVisibility['下降'] == true) {
      double dscLongitude = 0;
      if (housesData.ascmc[0] + 180 > 360) {
        dscLongitude = housesData.ascmc[0] - 180;
      } else {
        dscLongitude = housesData.ascmc[0] + 180;
      }
      final String dscSign = _getZodiacSign(dscLongitude);
      final int dscHouse =
          _calculateHouseFromLongitude(dscLongitude, housesDataDual);

      specialPoints.add(_createSpecialPointPosition(
        AstrologyConstants.DESCENDANT,
        dsc['name'],
        dsc['symbol'],
        dsc['color'],
        dscLongitude,
        dscSign,
        dscHouse,
        sunHouse: sunHouse,
      ));
    }

    // 處理天底
    if (planetVisibility == null || planetVisibility['天底'] == true) {
      double icLongitude = 0;
      if (housesData.ascmc[1] + 180 > 360) {
        icLongitude = housesData.ascmc[1] - 180;
      } else {
        icLongitude = housesData.ascmc[1] + 180;
      }
      final String icSign = _getZodiacSign(icLongitude);
      final int icHouse =
          _calculateHouseFromLongitude(icLongitude, housesDataDual);

      specialPoints.add(_createSpecialPointPosition(
        AstrologyConstants.IMUM_COELI,
        ic['name'],
        ic['symbol'],
        ic['color'],
        icLongitude,
        icSign,
        icHouse,
        sunHouse: sunHouse,
      ));
    }

    return specialPoints;
  }

  /// 計算特殊點位（上升點、中天、下降點、天底）
  List<PlanetPosition> _calculateSpecialPoints(
      HouseCuspData housesData, Map<String, bool>? planetVisibility,
      {int? sunHouse}) {
    final List<PlanetPosition> specialPoints = [];

    // 如果沒有提供太陽宮位，則嘗試尋找太陽在行星列表中的宮位
    // 這裡只是一個預設值，實際上應該在調用此方法時傳入太陽的宮位

    final asc = AstrologyConstants.PLANETS
        .firstWhere((planet) => planet['id'] == AstrologyConstants.ASCENDANT);
    final mc = AstrologyConstants.PLANETS
        .firstWhere((planet) => planet['id'] == AstrologyConstants.MIDHEAVEN);
    final dsc = AstrologyConstants.PLANETS
        .firstWhere((planet) => planet['id'] == AstrologyConstants.DESCENDANT);
    final ic = AstrologyConstants.PLANETS
        .firstWhere((planet) => planet['id'] == AstrologyConstants.IMUM_COELI);
    // 處理上升點
    if (planetVisibility == null || planetVisibility['上升'] == true) {
      final double ascLongitude = housesData.ascmc[0];
      final String ascSign = _getZodiacSign(ascLongitude);
      final int ascHouse =
          _calculateHouseFromLongitude(ascLongitude, housesData);

      specialPoints.add(_createSpecialPointPosition(
        AstrologyConstants.ASCENDANT,
        asc['name'],
        asc['symbol'],
        asc['color'],
        ascLongitude,
        ascSign,
        ascHouse,
        sunHouse: sunHouse,
      ));
    }

    // 處理中天
    if (planetVisibility == null || planetVisibility['中天'] == true) {
      final double mcLongitude = housesData.ascmc[1];
      final String mcSign = _getZodiacSign(mcLongitude);
      final int mcHouse = _calculateHouseFromLongitude(mcLongitude, housesData);

      specialPoints.add(_createSpecialPointPosition(
        AstrologyConstants.MIDHEAVEN,
        mc['name'],
        mc['symbol'],
        mc['color'],
        mcLongitude,
        mcSign,
        mcHouse,
        sunHouse: sunHouse,
      ));
    }

    // 處理下降點
    if (planetVisibility == null || planetVisibility['下降'] == true) {
      double dscLongitude = 0;
      if (housesData.ascmc[0] + 180 > 360) {
        dscLongitude = housesData.ascmc[0] - 180;
      } else {
        dscLongitude = housesData.ascmc[0] + 180;
      }
      final String dscSign = _getZodiacSign(dscLongitude);
      final int dscHouse =
          _calculateHouseFromLongitude(dscLongitude, housesData);

      specialPoints.add(_createSpecialPointPosition(
        AstrologyConstants.DESCENDANT,
        dsc['name'],
        dsc['symbol'],
        dsc['color'],
        dscLongitude,
        dscSign,
        dscHouse,
        sunHouse: sunHouse,
      ));
    }

    // 處理天底
    if (planetVisibility == null || planetVisibility['天底'] == true) {
      double icLongitude = 0;
      if (housesData.ascmc[1] + 180 > 360) {
        icLongitude = housesData.ascmc[1] - 180;
      } else {
        icLongitude = housesData.ascmc[1] + 180;
      }
      final String icSign = _getZodiacSign(icLongitude);
      final int icHouse = _calculateHouseFromLongitude(icLongitude, housesData);

      specialPoints.add(_createSpecialPointPosition(
        AstrologyConstants.IMUM_COELI,
        ic['name'],
        ic['symbol'],
        ic['color'],
        icLongitude,
        icSign,
        icHouse,
        sunHouse: sunHouse,
      ));
    }

    return specialPoints;
  }

  /// 創建特殊點位的 PlanetPosition 對象
  PlanetPosition _createSpecialPointPosition(int id, String name, String symbol,
      Color color, double longitude, String sign, int house,
      {int? sunHouse}) {
    // 判斷是否為白天出生
    // 如果提供了太陽宮位，則使用太陽宮位判斷
    // 否則默認使用特殊點的宮位（不太準確，但作為備用）
    final bool isDaytime =
        sunHouse != null ? _isDaytimeBirth(sunHouse) : _isDaytimeBirth(house);

    // 判斷宮位類型（始宮、續宮、果宮）
    final HouseType houseType = _calculateHouseType(house);

    // 判斷星座的陰陽性
    final bool isSignMasculine = _isSignMasculine(sign);

    // 特殊點位沒有日夜屬性，默認為日間行星
    // 如果是水星相關的特殊點位，可以在這裡加入特殊處理
    const bool isPlanetDiurnal = true;

    // 特殊點位沒有日夜區分狀態，默認為不在其區分位置
    const SectStatus sectStatus = SectStatus.outOfSect;

    // 特殊點位沒有尊貴力量狀態，使用普通狀態
    return PlanetPosition(
      id: id,
      name: name,
      symbol: symbol,
      color: color,
      longitude: longitude,
      latitude: 0.0,
      distance: 0.0,
      longitudeSpeed: 0.0,
      latitudeSpeed: 0.0,
      distanceSpeed: 0.0,
      sign: sign,
      house: house,
      dignity: PlanetDignity.peregrine,
      // 特殊點位使用普通狀態
      solarCondition: SolarCondition.free,
      // 特殊點位不考慮太陽狀態
      isDaytime: isDaytime,
      // 添加日夜區分
      houseType: houseType,
      // 添加宮位類型
      isPlanetDiurnal: isPlanetDiurnal,
      // 添加行星日夜屬性
      isSignMasculine: isSignMasculine,
      // 添加星座陰陽性
      sectStatus: sectStatus, // 添加日夜區分狀態
    );
  }

  /// 使用 Swiss Ephemeris 計算行星位置
  /// 並返回包含經度、緯度、距離和速度的 CoordinatesWithSpeed 對象
  /// final flagsMean = SwephFlag(SEFLG_SWIEPH | SEFLG_NONUT);  // 平均黃道
  /// final flagsTrue = SwephFlag(SEFLG_SWIEPH);                // 真實黃道（預設含章動）
  /// SwephFlag.SEFLG_SWIEPH | SwephFlag.SEFLG_NONUT | SwephFlag.SEFLG_SPEED. 平均回歸黃道（Mean Tropical）＝不含章動（nonut）
  CoordinatesWithSpeed sweCalcUt(double julianDay, HeavenlyBody body,
      {bool useMeanEcliptic = false}) {
    SwephFlag flags = useMeanEcliptic
        ? SwephFlag.SEFLG_SWIEPH | SwephFlag.SEFLG_NONUT | SwephFlag.SEFLG_SPEED
        : SwephFlag.SEFLG_SWIEPH | SwephFlag.SEFLG_SPEED;

    return Sweph.swe_calc_ut(julianDay, body, flags);
  }

  // static CoordinatesWithSpeed sweCalcUt(double julianDay, HeavenlyBody body) {
  //   // 使用 Swiss Ephemeris 計算行星位置
  //   // 平均回歸黃道（Mean Tropical）`SEFLG_SWIEPH
  //   // 真時回歸黃道（True Tropical）`SEFLG_SWIEPH（預設含章動，不加 SEFLG_NONUT）
  //   /**
  //    * final flagsMean = SwephFlag(SEFLG_SWIEPH | SEFLG_NONUT);  // 平均黃道
  //    * final flagsTrue = SwephFlag(SEFLG_SWIEPH);                // 真實黃道（預設含章動）
  //    */
  //   // TODO 要用平均回歸黃道 還是 真時回歸黃道？
  //   SwephFlag flagsMean =
  //       SwephFlag.SEFLG_SWIEPH | SwephFlag.SEFLG_NONUT | SwephFlag.SEFLG_SPEED;
  //   SwephFlag flagsTrue = SwephFlag.SEFLG_SWIEPH | SwephFlag.SEFLG_SPEED;
  //   CoordinatesWithSpeed result = Sweph.swe_calc_ut(
  //     julianDay,
  //     body,
  //     flagsTrue,
  //   );
  //   return result;
  // }

  /// 計算單個行星位置
  Future<PlanetPosition> _calculatePlanetPosition(
      Map<String, dynamic> planet,
      double julianDay,
      HouseCuspData? housesData,
      double ascendantDegree,
      bool isDaytime) async {
    // 使用 Swiss Ephemeris 計算行星位置
    CoordinatesWithSpeed result = sweCalcUt(
      julianDay,
      planet['body'] as HeavenlyBody,
    );

    // 從結果中獲取經度、緯度、距離
    final double longitude = result.longitude;

    // 獲取行星所在星座
    final sign = _getZodiacSign(longitude);

    // 獲取行星所在宮位
    int house;
    if (housesData != null && housesData.cusps.isNotEmpty) {
      // 如果有宮位數據，使用更精確的宮位計算方法
      house = _calculateHouseFromLongitude(longitude, housesData);
    } else {
      // 否則使用簡化的宮位計算方法
      house = _calculateHouse(longitude, ascendantDegree: ascendantDegree);
    }

    // 判斷是否為白天出生
    if (planet['id'] as int == AstrologyConstants.SUN) {
      isDaytime = _isDaytimeBirth(house);
    }

    // 計算行星尊貴力量狀態（需要在判斷日夜之後）
    final PlanetDignity dignity = _calculatePlanetDignity(
        planet['id'] as int, sign,
        longitude: longitude, isDaytime: isDaytime);

    // 判斷宮位類型（始宮、續宮、果宮）
    final HouseType houseType = _calculateHouseType(house);

    // 計算整宮制下的宮位類型和宮位數字
    HouseType? wholeSignHouseType;
    int? wholeSignHouse;
    if (housesData != null && housesData.ascmc.isNotEmpty) {
      // 獲取上升星座
      final double ascendantLongitude = housesData.ascmc[0];
      final String ascendantSign = _getZodiacSign(ascendantLongitude);

      // 計算整宮制下的宮位數字
      wholeSignHouse = _calculateWholeSignHouse(sign, ascendantSign);

      // 計算整宮制下的宮位類型
      wholeSignHouseType = _calculateWholeSignHouseType(sign, ascendantSign);
    }

    // 預設為自由狀態
    SolarCondition solarCondition = SolarCondition.free;

    // 存儲太陽經度，用於計算水星的日夜屬性和行星與太陽的關係
    double? sunLongitude;

    // 如果不是太陽本身，計算與太陽的關係
    if (planet['id'] as int != AstrologyConstants.SUN) {
      // 先獲取太陽的經度
      try {
        CoordinatesWithSpeed sunResult = sweCalcUt(
          julianDay,
          HeavenlyBody.SE_SUN,
        );
        sunLongitude = sunResult.longitude;

        // 計算行星與太陽的關係狀態
        solarCondition = _calculateSolarCondition(longitude, sunLongitude);
      } catch (e) {
        // 如果計算太陽位置出錯，保持預設狀態
        logger.e('計算太陽位置出錯: $e');
      }
    } else {
      // 如果是太陽本身，直接使用其經度
      sunLongitude = longitude;
    }

    // 判斷行星的日夜屬性
    bool isPlanetDiurnal;

    // 如果是水星，需要特殊處理
    if (planet['id'] as int == AstrologyConstants.MERCURY &&
        sunLongitude != null) {
      // 判斷水星的日夜屬性，傳入水星和太陽的經度
      isPlanetDiurnal = _isPlanetDiurnal(
        planet['id'] as int,
        mercuryLongitude: longitude,
        sunLongitude: sunLongitude,
      );
    } else {
      // 其他行星直接判斷
      isPlanetDiurnal = _isPlanetDiurnal(planet['id'] as int);
    }

    // 判斷星座的陰陽性
    final bool isSignMasculine = _isSignMasculine(sign);
    // 判斷行星的日夜區分狀態
    final SectStatus sectStatus = _calculateSectStatus(
        planet['id'] as int, sign, house, isPlanetDiurnal, isDaytime);

    return PlanetPosition(
      id: planet['id'] as int,
      name: planet['name'] as String,
      symbol: planet['symbol'] as String,
      color: planet['color'] as Color,
      longitude: result.longitude,
      latitude: result.latitude,
      distance: result.distance,
      longitudeSpeed: result.speedInLongitude,
      latitudeSpeed: result.speedInLatitude,
      distanceSpeed: result.speedInDistance,
      sign: sign,
      house: house,
      dignity: dignity,
      // 添加尊貴力量狀態
      solarCondition: solarCondition,
      // 添加與太陽的關係狀態
      isDaytime: isDaytime,
      // 添加日夜區分
      houseType: houseType,
      // 添加宮位類型
      wholeSignHouseType: wholeSignHouseType,
      // 添加整宮制下的宮位類型
      wholeSignHouse: wholeSignHouse,
      // 添加整宮制下的宮位數字
      isPlanetDiurnal: isPlanetDiurnal,
      // 添加行星日夜屬性
      isSignMasculine: isSignMasculine,
      // 添加星座陰陽性
      sectStatus: sectStatus, // 添加日夜區分狀態
    );
  }

  /// 計算南交點位置
  PlanetPosition _calculateSouthNodePosition(
    Map<String, dynamic> planet,
    List<PlanetPosition> positions,
    HouseCuspData? housesData,
    double ascendantDegree,
  ) {
    // 尋找北交點
    PlanetPosition northNode = positions.firstWhere(
        (element) => element.name == '北交點',
        orElse: () => throw Exception('找不到北交點'));

    // 計算南交點經度（北交點經度 + 180度，然後標準化到0-360度範圍）
    double longitude = (northNode.longitude + 180.0) % 360.0;

    // 獲取南交點所在星座
    final sign = _getZodiacSign(longitude);

    // 計算宮位
    int house;
    if (housesData != null && housesData.cusps.isNotEmpty) {
      house = _calculateHouseFromLongitude(longitude, housesData);
    } else {
      house = _calculateHouse(longitude, ascendantDegree: ascendantDegree);
    }

    // 判斷是否為白天出生
    final bool isDaytime = _isDaytimeBirth(house);

    // 計算南交點的尊貴力量狀態
    final PlanetDignity dignity = _calculatePlanetDignity(
        planet['id'] as int, sign,
        longitude: longitude, isDaytime: isDaytime);

    // 判斷宮位類型（始宮、續宮、果宮）
    final HouseType houseType = _calculateHouseType(house);

    // 計算整宮制下的宮位類型和宮位數字
    HouseType? wholeSignHouseType;
    int? wholeSignHouse;
    if (housesData != null && housesData.ascmc.isNotEmpty) {
      // 獲取上升星座
      final double ascendantLongitude = housesData.ascmc[0];
      final String ascendantSign = _getZodiacSign(ascendantLongitude);

      // 計算整宮制下的宮位數字
      wholeSignHouse = _calculateWholeSignHouse(sign, ascendantSign);

      // 計算整宮制下的宮位類型
      wholeSignHouseType = _calculateWholeSignHouseType(sign, ascendantSign);
    }

    // 預設為自由狀態
    SolarCondition solarCondition = SolarCondition.free;

    // 計算與太陽的關係
    try {
      // 先尋找太陽在行星列表中
      final sunPosition = positions.firstWhere(
        (p) => p.id == AstrologyConstants.SUN,
        orElse: () => PlanetPosition(
          id: AstrologyConstants.SUN,
          name: '太陽',
          symbol: '☉',
          longitude: 0,
          latitude: 0,
          distance: 0,
          longitudeSpeed: 0,
          latitudeSpeed: 0,
          distanceSpeed: 0,
          sign: '',
          house: 1,
        ),
      );

      // 計算行星與太陽的關係狀態
      solarCondition =
          _calculateSolarCondition(longitude, sunPosition.longitude);
    } catch (e) {
      // 如果計算太陽位置出錯，保持預設狀態
      logger.e('計算太陽位置出錯: $e');
    }

    // 存儲太陽經度，用於計算水星的日夜屬性
    double? sunLongitude;

    // 尋找太陽的經度
    for (final pos in positions) {
      if (pos.id == AstrologyConstants.SUN) {
        sunLongitude = pos.longitude;
        break;
      }
    }

    // 判斷行星的日夜屬性
    bool isPlanetDiurnal;

    // 如果是水星，需要特殊處理
    if (planet['id'] as int == AstrologyConstants.MERCURY &&
        sunLongitude != null) {
      // 判斷水星的日夜屬性，傳入水星和太陽的經度
      isPlanetDiurnal = _isPlanetDiurnal(
        planet['id'] as int,
        mercuryLongitude: longitude,
        sunLongitude: sunLongitude,
      );
    } else {
      // 其他行星直接判斷
      isPlanetDiurnal = _isPlanetDiurnal(planet['id'] as int);
    }

    // 判斷星座的陰陽性
    final bool isSignMasculine = _isSignMasculine(sign);

    // 判斷行星的日夜區分狀態
    final SectStatus sectStatus = _calculateSectStatus(
        planet['id'] as int, sign, house, isPlanetDiurnal, isDaytime);

    // 創建南交點位置對象
    return PlanetPosition(
      id: planet['id'] as int,
      name: planet['name'] as String,
      symbol: planet['symbol'] as String,
      color: planet['color'] as Color,
      longitude: longitude,
      latitude: -northNode.latitude,
      // 南交點緯度與北交點相反
      distance: northNode.distance,
      longitudeSpeed: -northNode.longitudeSpeed,
      // 速度與北交點相反
      latitudeSpeed: -northNode.latitudeSpeed,
      distanceSpeed: northNode.distanceSpeed,
      sign: sign,
      house: house,
      dignity: dignity,
      // 添加尊貴力量狀態
      solarCondition: solarCondition,
      // 添加與太陽的關係狀態
      isDaytime: isDaytime,
      // 添加日夜區分
      houseType: houseType,
      // 添加宮位類型
      wholeSignHouseType: wholeSignHouseType,
      // 添加整宮制下的宮位類型
      wholeSignHouse: wholeSignHouse,
      // 添加整宮制下的宮位數字
      isPlanetDiurnal: isPlanetDiurnal,
      // 添加行星日夜屬性
      isSignMasculine: isSignMasculine,
      // 添加星座陰陽性
      sectStatus: sectStatus, // 添加日夜區分狀態
    );
  }

  /// 返回默認的行星位置
  ///
  /// 當 Swiss Ephemeris 初始化失敗或計算出錯時使用
  List<PlanetPosition> _getDefaultPlanetPositions(double ascendantDegree,
      [HouseCuspData? housesData, Map<String, bool>? planetVisibility]) {
    final List<PlanetPosition> positions = [];

    // 先為太陽生成一個默認位置，以便判斷日夜盤
    int? sunHouse;
    if (housesData != null && housesData.cusps.isNotEmpty) {
      // 為太陽生成一個默認位置（基於行星ID的簡單分佈）
      const sunLongitude = (AstrologyConstants.SUN) * 30.0 % 360.0;
      sunHouse = _calculateHouseFromLongitude(sunLongitude, housesData);
    }

    // 處理特殊點位（上升點、中天、下降點、天底）
    if (housesData != null && housesData.cusps.isNotEmpty) {
      final specialPoints = _calculateSpecialPoints(
          housesData, planetVisibility,
          sunHouse: sunHouse);
      positions.addAll(specialPoints);
    }

    // 處理一般行星
    for (final planet in AstrologyConstants.PLANETS) {
      // 檢查行星是否應該顯示
      if (planetVisibility != null &&
          planetVisibility[planet['name'] as String] == false) {
        continue;
      }

      // 跳過特殊點位，因為已經在上面處理過了
      if (['上升', '中天', '下降', '天底'].contains(planet['name'])) {
        continue;
      }

      // 為每個行星生成一個默認位置（基於行星ID的簡單分佈）
      final longitude = (planet['id'] as int) * 30.0 % 360.0;
      final sign = _getZodiacSign(longitude);

      // 計算宮位
      int house;
      if (housesData != null && housesData.cusps.isNotEmpty) {
        house = _calculateHouseFromLongitude(longitude, housesData);
      } else {
        house = _calculateHouse(longitude, ascendantDegree: ascendantDegree);
      }

      // 判斷是否為白天出生
      final bool isDaytime = _isDaytimeBirth(house);

      // 計算行星尊貴力量狀態
      final PlanetDignity dignity = _calculatePlanetDignity(
          planet['id'] as int, sign,
          longitude: longitude, isDaytime: isDaytime);

      // 判斷宮位類型（始宮、續宮、果宮）
      final HouseType houseType = _calculateHouseType(house);

      // 計算整宮制下的宮位類型和宮位數字
      HouseType? wholeSignHouseType;
      int? wholeSignHouse;
      if (housesData != null && housesData.ascmc.isNotEmpty) {
        // 獲取上升星座
        final double ascendantLongitude = housesData.ascmc[0];
        final String ascendantSign = _getZodiacSign(ascendantLongitude);

        // 計算整宮制下的宮位數字
        wholeSignHouse = _calculateWholeSignHouse(sign, ascendantSign);

        // 計算整宮制下的宮位類型
        wholeSignHouseType = _calculateWholeSignHouseType(sign, ascendantSign);
      }

      // 存儲太陽經度，用於計算水星的日夜屬性
      double? sunLongitude;

      // 尋找太陽的經度
      if (planet['id'] as int == AstrologyConstants.MERCURY) {
        // 為水星尋找太陽的經度
        for (final p in AstrologyConstants.PLANETS) {
          if (p['id'] as int == AstrologyConstants.SUN) {
            // 為太陽生成一個默認位置（基於行星ID的簡單分佈）
            sunLongitude = (p['id'] as int) * 30.0 % 360.0;
            break;
          }
        }
      }

      // 判斷行星的日夜屬性
      bool isPlanetDiurnal;

      // 如果是水星，需要特殊處理
      if (planet['id'] as int == AstrologyConstants.MERCURY &&
          sunLongitude != null) {
        // 判斷水星的日夜屬性，傳入水星和太陽的經度
        isPlanetDiurnal = _isPlanetDiurnal(
          planet['id'] as int,
          mercuryLongitude: longitude,
          sunLongitude: sunLongitude,
        );
      } else {
        // 其他行星直接判斷
        isPlanetDiurnal = _isPlanetDiurnal(planet['id'] as int);
      }

      // 判斷星座的陰陽性
      final bool isSignMasculine = _isSignMasculine(sign);

      // 判斷行星的日夜區分狀態
      final SectStatus sectStatus = _calculateSectStatus(
          planet['id'] as int, sign, house, isPlanetDiurnal, isDaytime);

      // 創建默認行星位置
      positions.add(PlanetPosition(
        id: planet['id'] as int,
        name: planet['name'] as String,
        symbol: planet['symbol'] as String,
        color: planet['color'] as Color,
        longitude: longitude,
        latitude: 0.0,
        distance: 0.0,
        longitudeSpeed: 0.0,
        latitudeSpeed: 0.0,
        distanceSpeed: 0.0,
        sign: sign,
        house: house,
        dignity: dignity,
        // 添加尊貴力量狀態
        solarCondition: SolarCondition.free,
        // 默認為自由狀態
        isDaytime: isDaytime,
        // 添加日夜區分
        houseType: houseType,
        // 添加宮位類型
        wholeSignHouseType: wholeSignHouseType,
        // 添加整宮制下的宮位類型
        wholeSignHouse: wholeSignHouse,
        // 添加整宮制下的宮位數字
        isPlanetDiurnal: isPlanetDiurnal,
        // 添加行星日夜屬性
        isSignMasculine: isSignMasculine,
        // 添加星座陰陽性
        sectStatus: sectStatus, // 添加日夜區分狀態
      ));
    }
    return positions;
  }

  /// 計算阿拉伯點（特殊點）- 內部實現方法
  /// 根據不同的計算公式計算各種阿拉伯點的位置
  /// 已將計算邏輯拆分為多個輔助方法，使代碼更易於維護
  Future<List<PlanetPosition>> _calculateArabicPointsInternal(
      DateTime birthDateTime,
      List<PlanetPosition> planets,
      HouseCuspData? housesData,
      {required double latitude,
      required double longitude,
      Map<String, bool>? planetVisibility}) async {
    // 確保行星位置已經計算
    if (planets.isEmpty) {
      planets = await calculatePlanetPositions(
        birthDateTime,
        housesData: housesData,
        latitude: latitude,
        longitude: longitude,
      );
    }

    // 取得上升點、太陽和月亮的位置
    final double ascendant = housesData!.ascmc[0];

    // 對行星進行映射，方便快速查找
    Map<int, PlanetPosition> planetMap = {};
    for (final planet in planets) {
      planetMap[planet.id] = planet;
    }

    // 尋找太陽和月亮的位置
    final PlanetPosition? sun = planetMap[AstrologyConstants.SUN];
    final PlanetPosition? moon = planetMap[AstrologyConstants.MOON];

    if (sun == null || moon == null) {
      logger.d('無法計算阿拉伯點：缺少太陽或月亮位置');
      return [];
    }

    // 尋找其他行星位置
    final PlanetPosition? mercury = planetMap[AstrologyConstants.MERCURY];
    final PlanetPosition? venus = planetMap[AstrologyConstants.VENUS];
    final PlanetPosition? mars = planetMap[AstrologyConstants.MARS];
    final PlanetPosition? jupiter = planetMap[AstrologyConstants.JUPITER];
    final PlanetPosition? saturn = planetMap[AstrologyConstants.SATURN];

    // 判斷是白天還是夜晚出生
    // 白天出生：太陽在地平線上方（第7宮到第12宮之間）
    // 夜晚出生：太陽在地平線下方（第1宮到第6宮之間）
    final bool isDaytime = _isDaytimeBirth(sun.house);

    final List<PlanetPosition> arabicPoints = [];

    // 計算幸運點 (Lot of Fortune)
    double fortunePointLongitude;
    if (isDaytime) {
      // 白天公式：上升點 + 月亮 - 太陽
      fortunePointLongitude =
          _calculateArabicPoint(ascendant, moon.longitude, sun.longitude);
    } else {
      // 夜晚公式：上升點 + 太陽 - 月亮
      fortunePointLongitude =
          _calculateArabicPoint(ascendant, sun.longitude, moon.longitude);
    }

    // 計算基本的阿拉伯點（幸運點、精神點、擢升點、基礎點）
    List<PlanetPosition> basicArabicPoints = _calculateBasicArabicPoints(
        ascendant,
        sun,
        moon,
        fortunePointLongitude,
        isDaytime,
        housesData,
        planetVisibility);

    arabicPoints.addAll(basicArabicPoints);

    // 計算家庭相關的阿拉伯點（子女點、父親點、母親點、兒子點、女兒點、兄弟點）
    List<PlanetPosition> familyPoints = _calculateFamilyArabicPoints(
        ascendant, sun, moon, jupiter, saturn, venus, isDaytime, housesData);

    arabicPoints.addAll(familyPoints);

    // 計算職業和社會相關的阿拉伯點（成功點、職業點、愛情點、男性/女性婚姻點、金錢點）
    List<PlanetPosition> careerPoints = _calculateCareerArabicPoints(
        ascendant,
        sun,
        moon,
        jupiter,
        venus,
        mercury,
        saturn,
        fortunePointLongitude,
        isDaytime,
        housesData);

    arabicPoints.addAll(careerPoints);

    // 計算危險和挑戰相關的阿拉伯點（投機點、復仇點、危險點、死亡點、敵意點、盜竊點、悲劇點、暗殺點）
    List<PlanetPosition> challengePoints = _calculateChallengeArabicPoints(
        ascendant,
        planetMap,
        sun,
        moon,
        mars,
        mercury,
        jupiter,
        saturn,
        fortunePointLongitude,
        isDaytime,
        housesData);

    arabicPoints.addAll(challengePoints);

    // 計算其他阿拉伯點（高等教育點、朋友點、工作點）
    List<PlanetPosition> otherPoints = _calculateOtherArabicPoints(ascendant,
        sun, moon, mars, venus, mercury, jupiter, isDaytime, housesData);

    arabicPoints.addAll(otherPoints);
    arabicPoints.sort((h1, h2) => h1.id.compareTo(h2.id));
    return arabicPoints;
  }

  /// 計算基本的阿拉伯點（幸運點、精神點、擢升點、基礎點）
  List<PlanetPosition> _calculateBasicArabicPoints(
      double ascendant,
      PlanetPosition sun,
      PlanetPosition moon,
      double fortunePointLongitude,
      bool isDaytime,
      HouseCuspData housesData,
      Map<String, bool>? planetVisibility) {
    List<PlanetPosition> points = [];

    // 計算日月中點 (Sun-Moon Midpoint)
    if (planetVisibility != null && planetVisibility['日月中點']!) {
      double sunMoonMidpointLongitude =
          _calculateMidpoint(sun.longitude, moon.longitude);
      points.add(_createArabicPointPosition(
        AstrologyConstants.SUN_MOON_MIDPOINT,
        sunMoonMidpointLongitude,
        housesData,
      ));
    }

    // 計算宿命點 (Vertex Point)
    // 宿命點的計算需要緯度信息，這裡使用簡化公式
    // 實際的宿命點計算較為複雜，涉及到地平坐標系統
    if (planetVisibility != null && planetVisibility['宿命點']!) {
      double vertexLongitude = housesData.ascmc[3];
      points.add(_createArabicPointPosition(
        AstrologyConstants.VERTEX_POINT,
        vertexLongitude,
        housesData,
      ));
    }

    if (planetVisibility != null && planetVisibility['幸運點']!) {
      // 添加幸運點
      points.add(_createArabicPointPosition(
        AstrologyConstants.FORTUNE_POINT,
        fortunePointLongitude,
        housesData,
      ));
    }

    // 計算精神點 (Lot of Spirit)
    double spiritPointLongitude;
    if (isDaytime) {
      // 白天公式：上升點 + 太陽 - 月亮
      spiritPointLongitude =
          _calculateArabicPoint(ascendant, sun.longitude, moon.longitude);
    } else {
      // 夜晚公式：上升點 + 月亮 - 太陽
      spiritPointLongitude =
          _calculateArabicPoint(ascendant, moon.longitude, sun.longitude);
    }

    // 計算擢升點 (Exaltation)
    double exaltationPointLongitude;
    if (isDaytime) {
      // 白天公式：上升點 + 牡羊座19度 - 太陽
      // 牡羊座19度的絕對經度是19度
      exaltationPointLongitude = _calculateArabicPoint(
          ascendant, AstrologyConstants.ARIES_EXALTATION_DEGREE, sun.longitude);
    } else {
      // 夜晚公式：上升點 + 金牛座3度 - 月亮
      // 金牛座3度的絕對經度是30 + 3 = 33度
      double taurusExaltationDegree =
          AstrologyConstants.TAURUS_EXALTATION_DEGREE + 30.0;
      exaltationPointLongitude = _calculateArabicPoint(
          ascendant, taurusExaltationDegree, moon.longitude);
    }

    // 計算基礎點 (Basis)
    double basisPointLongitude;
    if (isDaytime) {
      // 白天公式：上升點 + 幸運點 - 精神點
      basisPointLongitude = _calculateArabicPoint(
          ascendant, fortunePointLongitude, spiritPointLongitude);
    } else {
      // 夜晚公式：上升點 + 精神點 - 幸運點
      basisPointLongitude = _calculateArabicPoint(
          ascendant, spiritPointLongitude, fortunePointLongitude);
    }

    // 將計算出的阿拉伯點添加到結果列表中
    if (planetVisibility != null && planetVisibility['精神點']!) {
      points.add(_createArabicPointPosition(
        AstrologyConstants.SPIRIT_POINT,
        spiritPointLongitude,
        housesData,
      ));
    }

    if (planetVisibility != null && planetVisibility['旺點']!) {
      points.add(_createArabicPointPosition(
        AstrologyConstants.EXALTATION_POINT,
        exaltationPointLongitude,
        housesData,
      ));
    }

    points.add(_createArabicPointPosition(
      AstrologyConstants.BASIS_POINT,
      basisPointLongitude,
      housesData,
    ));

    return points;
  }

  /// 計算單個阿拉伯點的經度
  /// 使用公式：上升點 + 加項 - 減項
  double _calculateArabicPoint(
      double ascendant, double addend, double subtrahend) {
    return _normalizeAngle(ascendant + addend - subtrahend);
  }

  /// 計算兩個行星的中點
  /// 中點是兩個行星位置的平均值
  double _calculateMidpoint(double longitude1, double longitude2) {
    // 處理跨越0度的情況
    double diff = (longitude2 - longitude1).abs();
    if (diff > 180) {
      // 如果差值大於180度，說明跨越了0度
      double midpoint = (longitude1 + longitude2 + 360) / 2;
      if (midpoint >= 360) {
        midpoint -= 360;
      }
      return midpoint;
    } else {
      // 正常情況下的中點計算
      return (longitude1 + longitude2) / 2;
    }
  }

  /// 計算職業和社會相關的阿拉伯點（成功點、職業點、愛情點、男性/女性婚姻點）
  List<PlanetPosition> _calculateCareerArabicPoints(
      double ascendant,
      PlanetPosition sun,
      PlanetPosition moon,
      PlanetPosition? jupiter,
      PlanetPosition? venus,
      PlanetPosition? mercury,
      PlanetPosition? saturn,
      double fortunePointLongitude,
      bool isDaytime,
      HouseCuspData housesData) {
    List<PlanetPosition> points = [];

    // 計算成功點 (Lot of Success) 上升 + 木星 - 福点
    if (jupiter != null) {
      double successPointLongitude;
      successPointLongitude = _calculateArabicPoint(
          ascendant, jupiter.longitude, fortunePointLongitude);

      points.add(_createArabicPointPosition(
        AstrologyConstants.SUCCESS_POINT,
        successPointLongitude,
        housesData,
      ));
    }

    // 計算職業點 (Lot of Profession)
    if (saturn != null) {
      // 職業點公式：上升 + 月亮 - 土星
      double professionPointLongitude =
          _calculateArabicPoint(ascendant, moon.longitude, saturn.longitude);

      points.add(_createArabicPointPosition(
        AstrologyConstants.PROFESSION_POINT,
        professionPointLongitude,
        housesData,
      ));
    }

    // 計算愛情點 (Lot of Love)
    if (venus != null) {
      double lovePointLongitude;
      if (isDaytime) {
        // 白天公式：上升點 + 金星 - 太陽
        lovePointLongitude =
            _calculateArabicPoint(ascendant, venus.longitude, sun.longitude);
      } else {
        // 夜晚公式：上升點 + 太陽 - 金星
        lovePointLongitude =
            _calculateArabicPoint(ascendant, sun.longitude, venus.longitude);
      }

      points.add(_createArabicPointPosition(
        AstrologyConstants.LOVE_POINT,
        lovePointLongitude,
        housesData,
      ));
    }

    // 計算男性婚姻點 (Lot of Marriage for Male)
    if (venus != null && saturn != null) {
      double marriageMalePointLongitude;
      // 男性婚姻點公式：上升點 + 金星 - 土星
      marriageMalePointLongitude =
          _calculateArabicPoint(ascendant, venus.longitude, saturn.longitude);

      points.add(_createArabicPointPosition(
        AstrologyConstants.MARRIAGE_MALE_POINT,
        marriageMalePointLongitude,
        housesData,
      ));
    }

    // 計算女性婚姻點 (Lot of Marriage for Female) 婚姻妇女I	上升 + 土星 - 金星 妇女婚姻II	上升 + 火星 - 月亮
    if (venus != null && saturn != null) {
      double marriageFemalePointLongitude;
      // 女性婚姻點公式：上升點 + 木星 - 金星
      marriageFemalePointLongitude =
          _calculateArabicPoint(ascendant, saturn.longitude, venus.longitude);

      points.add(_createArabicPointPosition(
        AstrologyConstants.MARRIAGE_FEMALE_POINT,
        marriageFemalePointLongitude,
        housesData,
      ));
    }

    // 計算婚姻點 (Part of Marriage)
    if (venus != null && saturn != null) {
      double marriagePointLongitude;
      // 使用通用的婚姻點公式 第七宮宮頭 ＋ 上升點 − 金星. effectiveHousesData['house1']
      marriagePointLongitude = _calculateArabicPoint(
          ascendant, housesData.cusps[7], venus.longitude);

      points.add(_createArabicPointPosition(
        AstrologyConstants.MARRIAGE_POINT,
        marriagePointLongitude,
        housesData,
      ));
    }

    // 計算債務點 (Part of Debt)
    if (mercury != null && saturn != null) {
      // 債務點公式：上升 + 水星 - 土星
      double debtPointLongitude =
          _calculateArabicPoint(ascendant, mercury.longitude, saturn.longitude);

      points.add(_createArabicPointPosition(
        AstrologyConstants.DEBT_POINT,
        debtPointLongitude,
        housesData,
      ));
    }

    // 計算知識點 (Part of Knowledge) 上升 + 月亮 - 水星
    // if (mercury != null) {
    //   double knowledgePointLongitude;
    //   // 白天公式：上升點 + 月亮 - 水星
    //   knowledgePointLongitude =
    //       _calculateArabicPoint(ascendant, moon.longitude, mercury.longitude);
    //
    //   points.add(_createArabicPointPosition(
    //     AstrologyConstants.KNOWLEDGE_POINT,
    //     knowledgePointLongitude,
    //     housesData,
    //   ));
    // }

    // 計算金錢點 (Lot of Money)
    // if (jupiter != null) {
    //   double moneyPointLongitude;
    //   if (isDaytime) {
    //     // 白天公式：上升點 + 木星 - 幸運點
    //     moneyPointLongitude = _calculateArabicPoint(
    //         ascendant, jupiter.longitude, fortunePointLongitude);
    //   } else {
    //     // 夜晚公式：上升點 + 幸運點 - 木星
    //     moneyPointLongitude = _calculateArabicPoint(
    //         ascendant, fortunePointLongitude, jupiter.longitude);
    //   }
    //
    //   points.add(_createArabicPointPosition(
    //     AstrologyConstants.MONEY_POINT,
    //     moneyPointLongitude,
    //     housesData,
    //   ));
    // }

    return points;
  }

  /// 計算危險和挑戰相關的阿拉伯點（投機點、復仇點、危險點、死亡點、敵意點、盜竊點、悲劇點、暗殺點）
  List<PlanetPosition> _calculateChallengeArabicPoints(
      double ascendant,
      Map<int, PlanetPosition> planetMap,
      PlanetPosition sun,
      PlanetPosition moon,
      PlanetPosition? mars,
      PlanetPosition? mercury,
      PlanetPosition? jupiter,
      PlanetPosition? saturn,
      double fortunePointLongitude,
      bool isDaytime,
      HouseCuspData housesData) {
    List<PlanetPosition> points = [];

    // 計算投機點 (Lot of Speculation) 投机I	上升 + H5 - 木星
    if (mercury != null && jupiter != null) {
      double speculationPointLongitude;
      // 白天公式：上升 + H5 - 木星
      speculationPointLongitude = _calculateArabicPoint(
          ascendant, housesData.cusps[5], jupiter.longitude);

      points.add(_createArabicPointPosition(
        AstrologyConstants.SPECULATION_POINT,
        speculationPointLongitude,
        housesData,
      ));
    }

    // 計算復仇點 (Lot of Nemesis)
    if (mars != null && saturn != null) {
      double nemesisPointLongitude;
      // 復仇點(Lot of Nemesis)：虛弱、放逐、毀滅、悲痛、殘酷、死亡。
      // 白天出生：復仇點=上升+福點-土星
      // 夜晚出生：復仇點=上升+土星-福點
      if (isDaytime) {
        nemesisPointLongitude = _calculateArabicPoint(
            ascendant, fortunePointLongitude, saturn.longitude);
      } else {
        nemesisPointLongitude = _calculateArabicPoint(
            ascendant, saturn.longitude, fortunePointLongitude);
      }

      points.add(_createArabicPointPosition(
        AstrologyConstants.NEMESIS_POINT,
        nemesisPointLongitude,
        housesData,
      ));
    }

    // 計算危險點 (Lot of Danger) 危险，暴力，债务	上升 + 水星 - 土星
    // if (mercury != null && saturn != null) {
    //   // 危險點公式：上升點 + 第8宮起點 - 火星
    //   double dangerPointLongitude =
    //       _calculateArabicPoint(ascendant, mercury.longitude, saturn.longitude);
    //
    //   points.add(_createArabicPointPosition(
    //     AstrologyConstants.DANGER_POINT,
    //     dangerPointLongitude,
    //     housesData,
    //   ));
    // }

    // 計算死亡點 (Lot of Death)
    // 死亡點公式：上升點 + 第8宮起點 - 月亮
    double house8Start = housesData.cusps[8]; // 默認第8宮起點為210度
    double deathPointLongitude =
        _calculateArabicPoint(ascendant, house8Start, moon.longitude);

    points.add(_createArabicPointPosition(
      AstrologyConstants.DEATH_POINT,
      deathPointLongitude,
      housesData,
    ));

    // 計算盜竊點 (Lot of Theft)
    if (mercury != null && mars != null && saturn != null) {
      double theftPointLongitude;
      // 偷竊點(Lot of Theft)：這一點是為數不多的幾個與上升點沒有任何聯系的標志點之一。
      // 白天出生：偷竊點=土星+火星-水星
      // 夜晚出生：偷竊點=土星+水星-火星
      if (isDaytime) {
        theftPointLongitude = _calculateArabicPoint(
            saturn.longitude, mars.longitude, mercury.longitude);
      } else {
        theftPointLongitude = _calculateArabicPoint(
            saturn.longitude, mercury.longitude, mars.longitude);
      }

      points.add(_createArabicPointPosition(
        AstrologyConstants.THEFT_POINT,
        theftPointLongitude,
        housesData,
      ));
    }

    // 計算悲劇點 (Lot of Tragedy) 上升 + 土星 - 太阳
    if (saturn != null) {
      // 悲劇點公式：上升點 + 土星 - 火星
      double tragedyPointLongitude =
          _calculateArabicPoint(ascendant, saturn.longitude, sun.longitude);

      points.add(_createArabicPointPosition(
        AstrologyConstants.TRAGEDY_POINT,
        tragedyPointLongitude,
        housesData,
      ));
    }

    final PlanetPosition? neptune = planetMap[AstrologyConstants.NEPTUNE];
    final PlanetPosition? uranus = planetMap[AstrologyConstants.URANUS];
    // 計算暗殺點 (Lot of Assassination)
    if (mars != null && neptune != null && uranus != null) {
      // 暗殺點公式：上升點 + 火星 - 土星 Mars	Neptune	Uranus
      double assassinationPointLongitude = _calculateArabicPoint(
          mars.longitude, neptune.longitude, uranus.longitude);

      points.add(_createArabicPointPosition(
        AstrologyConstants.ASSASSINATION_POINT,
        assassinationPointLongitude,
        housesData,
      ));
    }

    return points;
  }

  /// 計算其他阿拉伯點（高等教育點、朋友點、工作點）
  List<PlanetPosition> _calculateOtherArabicPoints(
      double ascendant,
      PlanetPosition sun,
      PlanetPosition moon,
      PlanetPosition? mars,
      PlanetPosition? venus,
      PlanetPosition? mercury,
      PlanetPosition? jupiter,
      bool isDaytime,
      HouseCuspData housesData) {
    List<PlanetPosition> points = [];

    // 計算高等教育點 (Lot of Higher Education)
    if (jupiter != null && mercury != null) {
      // 高等教育點公式：上升 + H9 - 水星 Higher Education Asc	9th	Mercury
      double educationPointLongitude = _calculateArabicPoint(
          ascendant, housesData.cusps[9], mercury.longitude);

      points.add(_createArabicPointPosition(
        AstrologyConstants.HIGHER_EDUCATION_POINT,
        educationPointLongitude,
        housesData,
      ));
    }

    // 計算朋友點 (Lot of Friends)
    if (venus != null) {
      double friendsPointLongitude;
      // 白天公式：上升點 + 月亮 - 金星
      friendsPointLongitude =
          _calculateArabicPoint(ascendant, moon.longitude, venus.longitude);

      points.add(_createArabicPointPosition(
        AstrologyConstants.FRIENDS_POINT,
        friendsPointLongitude,
        housesData,
      ));
    }

    // 計算工作點 (Lot of Work) 上升 + 木星 - 太阳
    if (jupiter != null) {
      double workPointLongitude;
      workPointLongitude =
          _calculateArabicPoint(ascendant, jupiter.longitude, sun.longitude);

      points.add(_createArabicPointPosition(
        AstrologyConstants.WORK_POINT,
        workPointLongitude,
        housesData,
      ));
    }

    return points;
  }

  /// 計算家庭相關的阿拉伯點（子女點、父親點、母親點、兒子點、女兒點、兄弟點）
  List<PlanetPosition> _calculateFamilyArabicPoints(
      double ascendant,
      PlanetPosition sun,
      PlanetPosition moon,
      PlanetPosition? jupiter,
      PlanetPosition? saturn,
      PlanetPosition? venus,
      bool isDaytime,
      HouseCuspData housesData) {
    List<PlanetPosition> points = [];

    // 計算子女點 (Part of Children)
    if (jupiter != null && saturn != null) {
      double childrenPointLongitude;
      if (isDaytime) {
        // 白天公式：上升點 + 土星 - 木星
        childrenPointLongitude = _calculateArabicPoint(
            ascendant, saturn.longitude, jupiter.longitude);
      } else {
        // 夜晚公式：上升點 + 木星 - 土星
        childrenPointLongitude = _calculateArabicPoint(
            ascendant, jupiter.longitude, saturn.longitude);
      }

      points.add(_createArabicPointPosition(
        AstrologyConstants.CHILDREN_POINT,
        childrenPointLongitude,
        housesData,
      ));
    }

    // 計算父親點 (Part of Father)
    if (saturn != null) {
      double fatherPointLongitude;
      if (isDaytime) {
        // 白天公式：上升點 + 土星 - 太陽
        fatherPointLongitude =
            _calculateArabicPoint(ascendant, saturn.longitude, sun.longitude);
      } else {
        // 夜晚公式：上升點 + 太陽 - 土星
        fatherPointLongitude =
            _calculateArabicPoint(ascendant, sun.longitude, saturn.longitude);
      }

      points.add(_createArabicPointPosition(
        AstrologyConstants.FATHER_POINT,
        fatherPointLongitude,
        housesData,
      ));
    }

    // 計算母親點 (Part of Mother)
    if (venus != null) {
      double motherPointLongitude;
      if (isDaytime) {
        // 白天公式：上升點 + 月亮 - 金星
        motherPointLongitude =
            _calculateArabicPoint(ascendant, moon.longitude, venus.longitude);
      } else {
        // 夜晚公式：上升點 + 金星 - 月亮
        motherPointLongitude =
            _calculateArabicPoint(ascendant, venus.longitude, moon.longitude);
      }

      points.add(_createArabicPointPosition(
        AstrologyConstants.MOTHER_POINT,
        motherPointLongitude,
        housesData,
      ));
    }

    // 計算兒子點 (Lot of Son)
    if (saturn != null && jupiter != null) {
      double sonPointLongitude;
      // 兒子點公式：上升點 + 木星 - 月亮
      sonPointLongitude =
          _calculateArabicPoint(ascendant, jupiter.longitude, moon.longitude);

      points.add(_createArabicPointPosition(
        AstrologyConstants.SON_POINT,
        sonPointLongitude,
        housesData,
      ));
    }

    // 計算女兒點 (Lot of Daughter)
    if (venus != null) {
      double daughterPointLongitude;
      // 女兒點公式：上升點 + 金星 - 月亮
      daughterPointLongitude =
          _calculateArabicPoint(ascendant, venus.longitude, moon.longitude);

      points.add(_createArabicPointPosition(
        AstrologyConstants.DAUGHTER_POINT,
        daughterPointLongitude,
        housesData,
      ));
    }

    // 計算兄弟點 (Lot of Brother)
    if (saturn != null && jupiter != null) {
      double brotherPointLongitude;
      // 公式：木星 ＋ 上升點 − 土星
      brotherPointLongitude =
          _calculateArabicPoint(ascendant, jupiter.longitude, saturn.longitude);

      points.add(_createArabicPointPosition(
        AstrologyConstants.BROTHER_POINT,
        brotherPointLongitude,
        housesData,
      ));
    }

    return points;
  }

  /// 判斷是否為白天出生
  /// 注意：此方法應傳入太陽的宮位，而非其他點的宮位
  bool _isDaytimeBirth(int sunHouse) {
    // 太陽在地平線上方為白天出生，即太陽在第7宮到第12宮之間
    // 在占星學中，第1宮是東方地平線，第7宮是西方地平線
    // 第10宮是天頂點，第4宮是天底點
    // 所以第7宮到第12宮是地平線上方，第1宮到第6宮是地平線下方
    return sunHouse >= 7 && sunHouse <= 12;
  }

  /// 判斷行星的日夜屬性
  /// 日間行星：太陽、木星、土星
  /// 夜間行星：月亮、金星、火星
  /// 水星則依據其相位與太陽的關係略做區分
  bool _isPlanetDiurnal(int planetId,
      {double? mercuryLongitude, double? sunLongitude}) {
    // 水星特殊判斷
    if (planetId == AstrologyConstants.MERCURY &&
        mercuryLongitude != null &&
        sunLongitude != null) {
      // 判斷水星是在太陽前方還是後方
      // 需要考慮黃道度數的循環性，使用最短距離並判斷方向

      // 計算水星相對於太陽的方向
      // 如果水星在太陽前方（即比太陽早升起），它為晨星，屬陽性傾向（日間行星）
      // 如果水星在太陽後方（即比太陽晚升起），它為暈星，屬陰性傾向（夜間行星）

      // 判斷水星是否在太陽前方
      bool isMorningMercury =
          _isMercuryBeforeSun(mercuryLongitude, sunLongitude);

      // 晨星（前方）為日間行星，暈星（後方）為夜間行星
      return isMorningMercury;
    }

    // 其他行星在常數定義中，返回其日夜屬性
    if (AstrologyConstants.PLANET_SECT_NATURE.containsKey(planetId)) {
      return AstrologyConstants.PLANET_SECT_NATURE[planetId]!;
    }

    // 如果不在常數定義中，默認為日間行星
    return true;
  }

  /// 判斷水星是否為晨星（Morning Star）
  /// 桨星：水星在太陽東方，即在太陽升起前先升起，屬陽性傾向（日間行星）
  /// 暈星：水星在太陽西方，即在太陽落下後才落下，屬陰性傾向（夜間行星）
  bool _isMercuryBeforeSun(double mercuryLongitude, double sunLongitude) {
    // 確保度數在 0-360 範圍內
    mercuryLongitude = mercuryLongitude % 360.0;
    sunLongitude = sunLongitude % 360.0;

    // 計算水星與太陽的直接差距
    double directDiff = mercuryLongitude - sunLongitude;

    // 考慮黃道度數的循環性
    if (directDiff > 180.0) {
      directDiff -= 360.0;
    } else if (directDiff < -180.0) {
      directDiff += 360.0;
    }

    // 在占星學中，水星為桨星（Morning Star）是指水星在太陽東方
    // 即水星的黃道度數小於太陽（逆時針方向），所以 directDiff < 0
    // 水星為暈星（Evening Star）是指水星在太陽西方
    // 即水星的黃道度數大於太陽（順時針方向），所以 directDiff > 0

    // 桨星（Morning Star）屬陽性傾向（日間行星），所以我們返回 directDiff < 0
    return directDiff < 0;
  }

  /// 判斷星座的陰陽性
  /// 陽性星座（陽陽陽）：白羊、雙子、獅子、天秤、射手、水瓶
  /// 陰性星座（陰陰陰）：金牛、巨蟹、處女、天蠍、摩羯、雙魚
  bool _isSignMasculine(String sign) {
    // 如果星座在常數定義中，返回其陰陽性
    if (AstrologyConstants.SIGN_POLARITY.containsKey(sign)) {
      return AstrologyConstants.SIGN_POLARITY[sign]!;
    }
    // 如果不在常數定義中，默認為陽性星座
    return true;
  }

  /// 判斷行星是否在其區分位置（In Sect）
  /// 判斷依據：
  /// 1. 行星的日夜屬性與目前星盤的日夜屬性是否一致
  /// 2. 行星是否在正確的半球（地平線上下）
  SectStatus _calculateSectStatus(int planetId, String sign, int house,
      bool isPlanetDiurnal, bool isDaytime) {
    // 判斷星座的陰陽性
    bool isSignMasculine = _isSignMasculine(sign);

    // 判斷行星是否在正確的半球
    bool isInCorrectHemisphere = false;

    // 日間盤：日間行星應該在地平線上方，夜間行星應該在地平線下方
    // 夜間盤：夜間行星應該在地平線上方，日間行星應該在地平線下方
    if (isDaytime) {
      // 日間盤
      if (isPlanetDiurnal) {
        // 日間行星應該在地平線上方（第7宮到第12宮）
        isInCorrectHemisphere = house >= 7 && house <= 12;
      } else {
        // 夜間行星應該在地平線下方（第1宮到第6宮）
        isInCorrectHemisphere = house >= 1 && house <= 6;
      }
    } else {
      // 夜間盤
      if (isPlanetDiurnal) {
        // 日間行星應該在地平線下方（第1宮到第6宮）
        isInCorrectHemisphere = house >= 1 && house <= 6;
      } else {
        // 夜間行星應該在地平線上方（第7宮到第12宮）
        isInCorrectHemisphere = house >= 7 && house <= 12;
      }
    }

    // 判斷行星是否在其區分位置
    bool isInSect = isInCorrectHemisphere;

    // 判斷行星是否形成場域（Hayz）
    // 場域條件：行星在其區分位置，且在與其日夜屬性相符的星座中
    // 日間行星應該在陽性星座，夜間行星應該在陰性星座
    bool isHayz = isInSect && (isPlanetDiurnal == isSignMasculine);

    // 返回日夜區分狀態
    if (isHayz) {
      return SectStatus.hayz;
    } else if (isInSect) {
      return SectStatus.inSect;
    } else {
      return SectStatus.outOfSect;
    }
  }

  /// 計算宮位類型（始宮、續宮、果宮）
  ///
  /// 在古典占星學中，宮位分為三種類型：
  /// - 始宮（Angular）：第1、第4、第7、第10宮，也稱為角宮
  /// - 續宮（Succedent）：第2、第5、第8、第11宮
  /// - 果宮（Cadent）：第3、第6、第9、第12宮
  ///
  /// 參數：
  /// - house：宮位編號（1-12）
  /// 返回：HouseType 枚舉值
  HouseType _calculateHouseType(int house) {
    // 確保宮位編號在 1-12 之間
    int normalizedHouse = house;
    if (normalizedHouse <= 0) normalizedHouse = 12; // 如果是 0，視為第 12 宮
    if (normalizedHouse > 12) normalizedHouse = normalizedHouse % 12;
    if (normalizedHouse == 0) normalizedHouse = 12;

    // 判斷宮位類型
    if (normalizedHouse == 1 ||
        normalizedHouse == 4 ||
        normalizedHouse == 7 ||
        normalizedHouse == 10) {
      return HouseType.angular; // 始宮/角宮
    } else if (normalizedHouse == 2 ||
        normalizedHouse == 5 ||
        normalizedHouse == 8 ||
        normalizedHouse == 11) {
      return HouseType.succedent; // 續宮
    } else {
      return HouseType.cadent; // 果宮
    }
  }

  /// 計算整宮制下的宮位類型（始宮、續宮、果宮）
  ///
  /// 在整宮制下，每個星座對應一個宮位，從上升星座開始計算
  /// 例如，如果上升點在天秤座，則天秤座是第1宮，天蝦座是第2宮，依此類推
  ///
  /// 參數：
  /// - sign：行星所在星座
  /// - ascendantSign：上升星座
  /// 返回：HouseType 枚舉值
  /// 計算整宮制下的宮位編號（1-12）
  int _calculateWholeSignHouse(String sign, String ascendantSign) {
    // 星座對應的編號（0-11）
    final Map<String, int> signIndices = {
      '牡羊座': 0,
      '金牛座': 1,
      '雙子座': 2,
      '巨蟹座': 3,
      '獅子座': 4,
      '處女座': 5,
      '天秤座': 6,
      '天蠍座': 7,
      '射手座': 8,
      '摩羯座': 9,
      '水瓶座': 10,
      '雙魚座': 11,
    };

    // 計算行星星座和上升星座的編號
    final int signIndex = signIndices[sign] ?? 0;
    final int ascendantIndex = signIndices[ascendantSign] ?? 0;

    // 計算整宮制下的宮位編號（1-12）
    return (signIndex - ascendantIndex + 12) % 12 + 1;
  }

  HouseType _calculateWholeSignHouseType(String sign, String ascendantSign) {
    // 計算整宮制下的宮位編號（1-12）
    int house = _calculateWholeSignHouse(sign, ascendantSign);

    // 判斷宮位類型
    if (house == 1 || house == 4 || house == 7 || house == 10) {
      return HouseType.angular; // 始宮/角宮
    } else if (house == 2 || house == 5 || house == 8 || house == 11) {
      return HouseType.succedent; // 續宮
    } else {
      return HouseType.cadent; // 果宮
    }
  }

  /// 計算行星與太陽的關係狀態
  ///
  /// 根據行星與太陽的角度差判斷行星是否在太陽核心、太陽光束傷害或太陽光束下
  /// 參數：
  /// - planetLongitude：行星經度
  /// - sunLongitude：太陽經度
  /// 返回：SolarCondition 枚舉值
  SolarCondition _calculateSolarCondition(
      double planetLongitude, double sunLongitude) {
    // 計算行星與太陽的角度差
    double diff = (planetLongitude - sunLongitude).abs();

    // 確保角度差在 0-180 度之間
    if (diff > 180) {
      diff = 360 - diff;
    }

    // 判斷行星與太陽的關係
    if (diff <= 0.2667) {
      return SolarCondition.cazimi; // 太陽核心
    } else if (diff <= 7.5) {
      return SolarCondition.combust; // 太陽光束傷害/焦傷
    } else if (diff <= 15.0) {
      return SolarCondition.underBeams; // 太陽光束下
    } else {
      return SolarCondition.free; // 自由
    }
  }

  /// 正規化角度，確保在 0-360 度之間
  double _normalizeAngle(double angle) {
    while (angle < 0) {
      angle += 360;
    }
    while (angle >= 360) {
      angle -= 360;
    }
    return angle;
  }

  /// 獨立方法：處理阿拉伯點計算和添加到行星列表
  /// 這個方法統一處理所有星盤類型的阿拉伯點計算邏輯
  Future<List<PlanetPosition>> processArabicPoints({
    required bool calculateArabicPoints,
    required DateTime birthDate,
    required List<PlanetPosition> planets,
    required HouseCuspData? houses,
    required double latitude,
    required double longitude,
    Map<String, bool>? planetVisibility,
  }) async {
    List<PlanetPosition> arabicPoints = [];

    if (calculateArabicPoints && houses != null) {
      try {
        arabicPoints = await _calculateArabicPointsInternal(
          birthDate,
          planets,
          houses,
          latitude: latitude,
          longitude: longitude,
          planetVisibility: planetVisibility,
        );
        logger.d('阿拉伯點計算完成，共 ${arabicPoints.length} 個點');

        // 將阿拉伯點添加到行星列表中（只添加 ID < 103 的點）
        for (var arabicPoint in arabicPoints) {
          if (arabicPoint.id < 103) {
            planets.add(arabicPoint);
          }
        }
        logger.d(
            '已將 ${arabicPoints.where((p) => p.id < 103).length} 個阿拉伯點添加到行星列表');
      } catch (e) {
        logger.d('計算阿拉伯點時出錯: $e');
        // 如果計算失敗，返回空列表
        arabicPoints = [];
      }
    } else {
      logger.d(
          '跳過阿拉伯點計算 - calculateArabicPoints: $calculateArabicPoints, houses: ${houses != null}');
    }

    return arabicPoints;
  }

  /// 計算阿拉伯點（公開方法，使用獨立處理邏輯）
  Future<List<PlanetPosition>> calculateArabicPoints(
    DateTime birthDate,
    List<PlanetPosition> planets,
    HouseCuspData houses, {
    required double latitude,
    required double longitude,
    Map<String, bool>? planetVisibility,
  }) async {
    return await _calculateArabicPointsInternal(
      birthDate,
      planets,
      houses,
      latitude: latitude,
      longitude: longitude,
      planetVisibility: planetVisibility,
    );
  }

  /// 創建阿拉伯點的 PlanetPosition 對象
  PlanetPosition _createArabicPointPosition(
      int pointId, double longitude, HouseCuspData housesData) {
    // 尋找阿拉伯點的定義
    Map<String, dynamic>? pointInfo;
    for (final point in AstrologyConstants.ARABIC_POINTS) {
      if (point['id'] == pointId) {
        pointInfo = point;
        break;
      }
    }

    if (pointInfo == null) {
      throw Exception('無法找到 ID 為 $pointId 的阿拉伯點定義');
    }

    // 計算星座和宮位
    final String sign = _getZodiacSign(longitude);
    final int house = _calculateHouseFromLongitude(longitude, housesData);

    // 判斷是否為白天出生
    final bool isDaytime = _isDaytimeBirth(house);

    // 判斷宮位類型（始宮、續宮、果宮）
    final HouseType houseType = _calculateHouseType(house);

    // 計算整宮制下的宮位類型和宮位數字
    HouseType? wholeSignHouseType;
    int? wholeSignHouse;
    if (housesData.ascmc.isNotEmpty) {
      // 獲取上升星座
      final double ascendantLongitude = housesData.ascmc[0];
      final String ascendantSign = _getZodiacSign(ascendantLongitude);

      // 計算整宮制下的宮位數字
      wholeSignHouse = _calculateWholeSignHouse(sign, ascendantSign);

      // 計算整宮制下的宮位類型
      wholeSignHouseType = _calculateWholeSignHouseType(sign, ascendantSign);
    }

    // 判斷星座的陰陽性
    final bool isSignMasculine = _isSignMasculine(sign);

    // 阿拉伯點沒有日夜屬性，默認為日間行星
    // 如果是水星相關的阿拉伯點，可以在這裡加入特殊處理
    const bool isPlanetDiurnal = true;

    // 阿拉伯點沒有日夜區分狀態，默認為不在其區分位置
    const SectStatus sectStatus = SectStatus.outOfSect;

    return PlanetPosition(
      id: pointId,
      name: pointInfo['name'] as String,
      symbol: pointInfo['symbol'] as String,
      color: pointInfo['color'] as Color,
      longitude: longitude,
      latitude: 0.0,
      // 阿拉伯點沒有緯度
      distance: 0.0,
      // 阿拉伯點沒有距離
      longitudeSpeed: 0.0,
      // 阿拉伯點沒有速度
      latitudeSpeed: 0.0,
      distanceSpeed: 0.0,
      sign: sign,
      house: house,
      dignity: PlanetDignity.peregrine,
      // 阿拉伯點使用普通狀態
      solarCondition: SolarCondition.free,
      // 阿拉伯點不考慮太陽狀態
      isDaytime: isDaytime,
      // 添加日夜區分
      houseType: houseType,
      // 添加宮位類型
      wholeSignHouseType: wholeSignHouseType,
      // 添加整宮制下的宮位類型
      wholeSignHouse: wholeSignHouse,
      // 添加整宮制下的宮位數字
      isPlanetDiurnal: isPlanetDiurnal,
      // 添加行星日夜屬性
      isSignMasculine: isSignMasculine,
      // 添加星座陰陽性
      sectStatus: sectStatus, // 添加日夜區分狀態
    );
  }

  /// 計算行星之間的互容接納關係，不考慮相位
  List<AspectInfo> calculateReceptions(List<PlanetPosition> planets) {
    final List<AspectInfo> receptions = [];

    // 計算行星之間的互容接納關係
    for (int i = 0; i < planets.length; i++) {
      for (int j = i + 1; j < planets.length; j++) {
        final planet1 = planets[i];
        final planet2 = planets[j];
        Map<String, dynamic> receptionInfo = {
          'receptionType': ReceptionType.none,
          'description': "",
        };
        if (planet1.id < 7 && planet2.id < 7) {
          // 不考慮相位，所以傳入 hasAspect = false
          receptionInfo = _checkReception(planet1, planet2, hasAspect: false);
        }
        // 檢查互容接納狀態
        final receptionType = receptionInfo['receptionType'] as ReceptionType;

        // 如果存在互容接納關係，則添加到結果中
        if (receptionType != ReceptionType.none) {
          receptions.add(AspectInfo(
            planet1: planet1,
            planet2: planet2,
            aspect: '接納',
            // 使用接納作為相位名稱
            shortZh: '接納',
            symbol: '',
            // 使用雙向箭頭符號
            angle: 0,
            // 不考慮角度
            orb: 0,
            // 不考慮容許度
            receptionType: receptionType,
            receptionDescription: receptionInfo['description'] as String?,
            direction: null, // 互容接納關係不考慮入相或出相
          ));
        }
      }
    }

    return receptions;
  }

  /// 計算相位
  List<AspectInfo> calculateAspects(
    List<PlanetPosition> planets, {
    Map<String, double>? aspectOrbs,
  }) {
    final List<AspectInfo> aspects = [];

    // 計算行星之間的相位
    for (int i = 0; i < planets.length; i++) {
      for (int j = 0; j < planets.length; j++) {
        final planet1 = planets[i];
        final planet2 = planets[j];

        if (planet1.id == planet2.id) continue;

        // 計算兩個行星之間的角度差
        double angleDiff = planet2.longitude - planet1.longitude;

        // 取角度差的絕對值來檢查相位
        double absAngleDiff = angleDiff.abs();
        if (absAngleDiff > 180) {
          absAngleDiff = 360 - absAngleDiff;
        }

        // 檢查是否形成相位
        for (final aspectType in AstrologyConstants.ASPECTS) {
          final int angle = aspectType['angle'] as int;
          // 使用自定義容許度或默認容許度
          final double orb = aspectOrbs?[aspectType['name'] as String] ??
              aspectType['orb'] as double;

          if ((absAngleDiff - angle).abs() <= orb) {
            // 檢查行星互容接納狀態
            Map<String, dynamic> receptionInfo = {
              'receptionType': ReceptionType.none,
              'description': "",
            };
            if (planet1.id < 7 && planet2.id < 7) {
              // 已經形成相位，所以傳入 hasAspect = true
              receptionInfo =
                  _checkReception(planet1, planet2, hasAspect: true);
            }
            // 判斷相位方向（入相或出相）
            AspectDirection aspectDirection =
                getAspectDirection(planet2, planet1, angle.toDouble());
            AspectInfo aspectInfo = AspectInfo(
              planet1: planet1,
              planet2: planet2,
              aspect: aspectType['name'] as String,
              shortZh: aspectType['shortZh'] as String,
              symbol: aspectType['symbol'] as String,
              angle: angle,
              orb: (absAngleDiff - angle).abs(),
              receptionType: receptionInfo['receptionType'] as ReceptionType,
              receptionDescription: receptionInfo['description'] as String?,
              direction: aspectDirection,
            );

            if (!planet1.aspects.contains(aspectInfo)) {
              planet1.aspects.add(aspectInfo);
            }
            // if (!planet2.aspects.contains(aspectInfo)) {
            //   planet2.aspects.add(aspectInfo);
            // }
            aspects.add(aspectInfo);
            break;
          }
        }
      }
    }

    return aspects;
  }

  /// 計算雙重星盤的相位（本命盤與行運盤之間的相位）
  List<AspectInfo> calculateDualChartAspects(
    List<PlanetPosition> planetsA,
    List<PlanetPosition> planetsB, {
    Map<String, double>? aspectOrbs,
  }) {
    final List<AspectInfo> aspects = [];

    // 計算本命盤與行運盤行星之間的相位
    for (int i = 0; i < planetsA.length; i++) {
      for (int j = 0; j < planetsB.length; j++) {
        final planetA = planetsA[i];
        final planetB = planetsB[j];
        // if (planetA.id > planetB.id) continue;
        // 計算兩個行星之間的角度差
        double angleDiff = planetB.longitude - planetA.longitude;

        // 取角度差的絕對值來檢查相位
        double absAngleDiff = angleDiff.abs();
        if (absAngleDiff > 180) {
          absAngleDiff = 360 - absAngleDiff;
        }

        // 檢查是否形成相位
        for (final aspectType in AstrologyConstants.ASPECTS) {
          final int angle = aspectType['angle'] as int;
          // 使用自定義容許度或默認容許度
          final double orb = aspectOrbs?[aspectType['name'] as String] ??
              aspectType['orb'] as double;

          if ((absAngleDiff - angle).abs() <= orb) {
            // 檢查行星互容接納狀態
            Map<String, dynamic> receptionInfo = {
              'receptionType': ReceptionType.none,
              'description': "",
            };
            if (planetA.id < 7 && planetB.id < 7) {
              // 已經形成相位，所以傳入 hasAspect = true
              receptionInfo =
                  _checkReception(planetA, planetB, hasAspect: true);
            }

            // 判斷相位方向（入相或出相）
            AspectDirection aspectDirection =
                getAspectDirection(planetB, planetA, angle.toDouble());

            AspectInfo aspectInfo = AspectInfo(
              planet1: planetA,
              planet2: planetB,
              aspect: aspectType['name'] as String,
              shortZh: aspectType['shortZh'] as String,
              symbol: aspectType['symbol'] as String,
              angle: angle,
              orb: (absAngleDiff - angle).abs(),
              receptionType: receptionInfo['receptionType'] as ReceptionType,
              receptionDescription: receptionInfo['description'] as String?,
              direction: aspectDirection, // 添加入相或出相的信息
            );

            // logger.d(
            //     '添加相位: ${planetA.name} ${aspectInfo.shortZh} ${planetB.name}');
            if (!planetA.aspects.contains(aspectInfo)) {
              planetA.aspects.add(aspectInfo);
            }
            if (!planetB.aspects.contains(aspectInfo)) {
              planetB.aspects.add(aspectInfo);
            }
            aspects.add(aspectInfo);
            break;
          }
        }
      }
    }

    return aspects;
  }

  /// 獲取星座
  String _getZodiacSign(double longitude) {
    final int signIndex = (longitude / 30).floor() % 12;
    return AstrologyConstants.ZODIAC_SIGNS[signIndex];
  }

  /// 計算行星尊貴力量狀態（廟旺陷弱三分界十）
  ///
  /// 根據行星和其所在星座判斷其尊貴力量狀態
  /// 返回 PlanetDignity 枚舉值
  ///
  /// 先天尊貴等級（由高到低）：
  /// 1. 廟（Domicile）- 行星在自己主管的星座
  /// 2. 旺（Exaltation）- 行星在旺星座
  /// 3. 三分（Triplicity）- 行星是該星座的三分主星
  /// 4. 界（Terms）- 行星是該度數的界主星
  /// 5. 十（Decan）- 行星是該十度的主星
  /// 6. 陷（Detriment）- 行星在陷星座
  /// 7. 弱（Fall）- 行星在弱星座
  /// 8. 普通（Normal）- 無特殊尊貴狀態
  PlanetDignity _calculatePlanetDignity(int planetId, String sign,
      {double? longitude, bool? isDaytime}) {
    // 如果不是主要行星，返回普通狀態
    if (!AstrologyConstants.PLANET_DIGNITIES.containsKey(planetId)) {
      return PlanetDignity.peregrine;
    }

    // 獲取行星的廟旺陷弱定義
    final Map<String, String> dignities =
        AstrologyConstants.PLANET_DIGNITIES[planetId]!;

    // 1. 檢查廟狀態（行星在自己的主管星座）- 最高等級
    final String domicileStr = dignities[AstrologyConstants.DOMICILE] ?? '';
    final List<String> domicileSigns = domicileStr.split(',');
    if (domicileSigns.contains(sign)) {
      return PlanetDignity.domicile;
    }

    // 2. 檢查旺狀態（行星在旺星座）- 第二高等級
    final String exaltationStr = dignities[AstrologyConstants.EXALTATION] ?? '';
    if (exaltationStr == sign) {
      return PlanetDignity.exaltation;
    }

    // 3. 檢查陷狀態（行星在陷星座）- 負面狀態，優先於其他正面狀態
    final String detrimentStr = dignities[AstrologyConstants.DETRIMENT] ?? '';
    final List<String> detrimentSigns = detrimentStr.split(',');
    if (detrimentSigns.contains(sign)) {
      return PlanetDignity.detriment;
    }

    // 4. 檢查弱狀態（行星在弱星座）- 負面狀態，優先於其他正面狀態
    final String fallStr = dignities[AstrologyConstants.FALL] ?? '';
    if (fallStr == sign) {
      return PlanetDignity.fall;
    }

    // 5. 檢查三分主星狀態（Dorothean System）
    if (AstrologyConstants.ZODIAC_TRIPLICITIES.containsKey(sign)) {
      final Map<String, int> triplicityRulers =
          AstrologyConstants.ZODIAC_TRIPLICITIES[sign]!;
      final int? dayRuler = triplicityRulers['day'];
      final int? nightRuler = triplicityRulers['night'];
      final int? participatingRuler = triplicityRulers['participating'];

      // 檢查是否為日間主星、夜間主星或通用主星
      if (isDaytime != null) {
        // 如果有日夜信息，優先檢查對應的日夜主星
        if ((isDaytime && dayRuler == planetId) ||
            (!isDaytime && nightRuler == planetId)) {
          return PlanetDignity.triplicity;
        }
      }

      // 檢查是否為通用主星（無論日夜）
      if (participatingRuler == planetId) {
        return PlanetDignity.triplicity;
      }

      // 如果沒有日夜信息，檢查是否為日間或夜間主星
      if (isDaytime == null &&
          (dayRuler == planetId || nightRuler == planetId)) {
        return PlanetDignity.triplicity;
      }
    }

    // 6. 檢查界主星狀態（需要經度信息）
    if (longitude != null) {
      final int? termRuler = calculateTermRuler(longitude);
      if (termRuler == planetId) {
        return PlanetDignity.terms;
      }
    }

    // 7. 檢查十度主星狀態（需要經度信息）
    if (longitude != null &&
        AstrologyConstants.ZODIAC_DECANS.containsKey(sign)) {
      final double degreeInSign = longitude % 30.0;
      final Map<String, int> decans = AstrologyConstants.ZODIAC_DECANS[sign]!;

      // 找到對應的十度主星
      int? decanRuler;
      if (degreeInSign >= 0 && degreeInSign < 10) {
        decanRuler = decans['0.0'];
      } else if (degreeInSign >= 10 && degreeInSign < 20) {
        decanRuler = decans['10.0'];
      } else if (degreeInSign >= 20 && degreeInSign < 30) {
        decanRuler = decans['20.0'];
      }

      if (decanRuler == planetId) {
        return PlanetDignity.decan;
      }
    }

    // 如果以上都不符合，返回普通狀態
    return PlanetDignity.peregrine;
  }

  /// 判斷行星是否主管指定星座
  ///
  /// 檢查行星是否是指定星座的主管行星
  /// 返回布爾值，表示是否主管
  bool _isPlanetRulerOfSign(int planetId, String sign) {
    // 如果不是主要行星，返回 false
    if (!AstrologyConstants.PLANET_DIGNITIES.containsKey(planetId)) {
      return false;
    }

    // 獲取行星的廟定義
    final Map<String, String> dignities =
        AstrologyConstants.PLANET_DIGNITIES[planetId]!;
    final String domicileStr = dignities[AstrologyConstants.DOMICILE] ?? '';
    final List<String> domicileSigns = domicileStr.split(',');

    // 檢查行星是否主管指定星座
    return domicileSigns.contains(sign);
  }

  /// 計算指定度數的星座界主星
  ///
  /// 根據度數和星座，返回該度數的界主星ID
  /// 參數：
  /// - longitude: 黃道經度（0-360度）
  /// 返回：界主星的行星ID
  int? calculateTermRuler(double longitude) {
    // 將經度轉換為星座內的度數（0-30度）
    final double degreeInSign = longitude % 30.0;

    // 獲取星座名稱
    final String sign = _getZodiacSign(longitude);

    // 獲取該星座的界主星定義
    final Map<String, int>? terms = AstrologyConstants.ZODIAC_TERMS[sign];
    if (terms == null) return null;

    // 將字符串key轉換為double並排序
    final List<int> degrees = terms.keys.map((key) => int.parse(key)).toList()
      ..sort();

    // 找到對應的界主星
    int? termRuler;
    for (int degree in degrees) {
      if (degreeInSign >= degree) {
        termRuler = terms[degree.toString()];
      } else {
        break;
      }
    }

    return termRuler;
  }

  /// 界主星配置法計算
  ///
  /// 根據上升星座和緯度計算界主星配置的時間
  /// 參數：
  /// - ascendantLongitude: 上升點經度
  /// - latitude: 緯度
  /// - isNorth: 是否為北半球
  /// 返回：界主星配置法的計算結果
  TermRulerProgressionResult calculateTermRulerProgression(
      double ascendantLongitude, double latitude, bool isNorth) {
    // 1. 取得上升星座
    final String ascendantSign = _getZodiacSign(ascendantLongitude);

    // 2. 取得上升界主星
    final int? ascendantTermRuler = calculateTermRuler(ascendantLongitude);

    if (ascendantTermRuler == null) {
      throw Exception('無法計算上升界主星');
    }

    // 3. 利用赤經上升時間表來抓出這個星座與緯度每一度需要多少時間
    final List<double> timeData = AscensionTable.getTimeByLatitudeAndSign(
        latitude, ascendantSign, isNorth);

    final double timePerDegree = timeData[1]; // 每度所需時間（天）
    final double totalSignTime = timeData[0]; // 走完整個星座所需時間（天）

    // 獲取緯度索引調試資訊
    final Map<String, dynamic> latitudeDebugInfo =
        AscensionTable.getLatitudeIndexDebugInfo(latitude.abs());

    // 4. 計算到下一個界需要多少時間與準確日期
    final NextTermInfo nextTermInfo = _calculateNextTermInfo(
        ascendantLongitude, ascendantSign, timePerDegree);

    return TermRulerProgressionResult(
      ascendantSign: ascendantSign,
      ascendantTermRuler: ascendantTermRuler,
      ascendantTermRulerName: _getPlanetNameById(ascendantTermRuler),
      timePerDegree: timePerDegree,
      totalSignTime: totalSignTime,
      latitude: latitude,
      isNorth: isNorth,
      latitudeDebugInfo: latitudeDebugInfo,
      nextTermInfo: nextTermInfo,
      currentDegreeInSign: ascendantLongitude % 30.0,
    );
  }

  /// 計算到下一個界的資訊（逆時針方向 - 按度數遞增）
  NextTermInfo _calculateNextTermInfo(
      double ascendantLongitude, String ascendantSign, double timePerDegree) {
    final double degreeInSign = ascendantLongitude % 30.0;

    // 獲取該星座的界主星定義
    final Map<String, int>? terms =
        AstrologyConstants.ZODIAC_TERMS[ascendantSign];
    if (terms == null) {
      throw Exception('無法找到星座 $ascendantSign 的界主星定義');
    }

    // 將字符串key轉換為double並正序排列（逆時針 = 度數遞增）
    final List<double> degrees = terms.keys
        .map((key) => double.parse(key))
        .toList()
      ..sort(); // 正序排列：0, 6, 12, 20, 25

    // 找到下一個界的起始度數（逆時針 = 度數遞增方向）
    double? nextTermDegree;
    int? nextTermRuler;

    // 逆時針查找：找到大於當前度數的最小度數
    for (final degree in degrees) {
      if (degree > degreeInSign) {
        nextTermDegree = degree;
        nextTermRuler = terms[degree.toString()];
        break;
      }
    }

    // 如果沒有找到下一個界，表示已經是最後一個界，下一個界在下一個星座的第一個界
    if (nextTermDegree == null) {
      final String nextSign = _getNextZodiacSign(ascendantSign);
      final Map<String, int>? nextSignTerms =
          AstrologyConstants.ZODIAC_TERMS[nextSign];
      if (nextSignTerms != null) {
        // 找到下一個星座的第一個界
        final List<double> nextDegrees = nextSignTerms.keys
            .map((key) => double.parse(key))
            .toList()
          ..sort(); // 正序排列

        if (nextDegrees.isNotEmpty) {
          nextTermDegree = nextDegrees.first; // 最小的度數（第一個界）
          nextTermRuler = nextSignTerms[nextTermDegree.toString()];

          // 計算跨星座的度數差：(30 - 當前度數) + 下一個星座的第一界度數
          final double degreesToNextSign = 30.0 - degreeInSign; // 到星座結束的度數
          final double degreesInNextSign = nextTermDegree; // 下一個星座中需要走的度數
          final double totalDegrees = degreesToNextSign + degreesInNextSign;

          final double timeToNextTerm = totalDegrees * timePerDegree;

          return NextTermInfo(
            nextTermDegree: nextTermDegree,
            nextTermRuler: nextTermRuler,
            nextTermRulerName: nextTermRuler != null
                ? _getPlanetNameById(nextTermRuler)
                : '未知',
            degreesToNextTerm: totalDegrees,
            timeToNextTermDays: timeToNextTerm,
            // AscensionTable 單位是天
            crossesSign: true,
            targetSign: nextSign,
          );
        }
      }
    }

    // 計算到下一個界需要的度數（逆時針 = 度數遞增）
    final double degreesToNextTerm = (nextTermDegree ?? 30.0) - degreeInSign;

    // 計算到下一個界需要的時間（天）
    final double timeToNextTerm = degreesToNextTerm * timePerDegree;

    return NextTermInfo(
      nextTermDegree: nextTermDegree,
      nextTermRuler: nextTermRuler,
      nextTermRulerName:
          nextTermRuler != null ? _getPlanetNameById(nextTermRuler) : '未知',
      degreesToNextTerm: degreesToNextTerm,
      timeToNextTermDays: timeToNextTerm,
      // AscensionTable 單位是天
      crossesSign: false,
      targetSign: ascendantSign,
    );
  }

  /// 根據行星ID獲取行星名稱
  String _getPlanetNameById(int planetId) {
    for (final planet in AstrologyConstants.PLANETS) {
      if (planet['id'] == planetId) {
        return planet['name'] as String;
      }
    }
    return '未知行星';
  }

  /// 獲取下一個星座
  String _getNextZodiacSign(String currentSign) {
    final int currentIndex =
        AstrologyConstants.ZODIAC_SIGNS.indexOf(currentSign);
    if (currentIndex == -1) return AstrologyConstants.ZODIAC_SIGNS[0];

    final int nextIndex =
        (currentIndex + 1) % AstrologyConstants.ZODIAC_SIGNS.length;
    return AstrologyConstants.ZODIAC_SIGNS[nextIndex];
  }

  /// 獲取星座界主星的分佈信息
  ///
  /// 返回每個星座的界主星分佈，用於在星盤外圈繪製
  /// 返回格式：Map<星座名稱, List<界主星信息>>
  Map<String, List<Map<String, dynamic>>> getZodiacTermsDistribution() {
    final Map<String, List<Map<String, dynamic>>> distribution = {};

    for (final signEntry in AstrologyConstants.ZODIAC_TERMS.entries) {
      final String sign = signEntry.key;
      final Map<String, int> terms = signEntry.value;

      final List<Map<String, dynamic>> termsList = [];

      // 創建度數到原始鍵的映射，避免精度問題
      final Map<double, String> degreeToKey = {};
      for (final entry in terms.entries) {
        degreeToKey[double.parse(entry.key)] = entry.key;
      }

      final List<double> degrees = degreeToKey.keys.toList()..sort();

      for (int i = 0; i < degrees.length; i++) {
        final double startDegree = degrees[i];
        final double endDegree = i < degrees.length - 1 ? degrees[i + 1] : 30.0;

        // 使用原始鍵來查找行星 ID，避免精度問題
        final String originalKey = degreeToKey[startDegree]!;
        final int? planetId = terms[originalKey];

        if (planetId == null) {
          continue; // 跳過無效的條目
        }

        // 獲取行星信息
        final planet = AstrologyConstants.PLANETS.firstWhere(
          (p) => p['id'] == planetId,
          orElse: () => {'name': '未知', 'symbol': '?', 'color': Colors.grey},
        );

        termsList.add({
          'startDegree': startDegree,
          'endDegree': endDegree,
          'planetId': planetId,
          'planetName': planet['name'],
          'planetSymbol': planet['symbol'],
          'planetColor': planet['color'],
        });
      }

      distribution[sign] = termsList;
    }

    return distribution;
  }

  /// 判斷相位是入相還是出相
  /// https://groups.io/g/swisseph/topic/logic_in_programming_the/107562674
  /// 根據行星的速度和位置判斷相位是入相還是出相
  /// 使用 swisseph 推薦的高效算法：
  /// 1. 計算兩個行星之間的角度
  /// 2. 計算軌道差（orbis）：負值表示角度小於精確角度，正值表示角度大於精確角度
  /// 3. 根據軌道差和行星速度判斷方向：
  ///    - 如果 orbis < 0 且 vel1 > vel2，則為出相
  ///    - 如果 orbis > 0 且 vel1 < vel2，則為出相
  ///    - 其他情況為入相
  ///
  /// 入相：相位角度正在接近精確角度
  /// 出相：相位角度正在遠離精確角度
  AspectDirection getAspectDirection(
      PlanetPosition position1, PlanetPosition position2, double aspectAngle) {
    // 計算兩個行星之間的角度（確保在0-360範圍內）
    double differenceAngle = position1.longitude - position2.longitude;

    // 計算軌道差（orbis）：當前角度與精確相位角度的差值
    // orbis < 0：當前角度小於精確角度
    // orbis > 0：當前角度大於精確角度
    double orbis = aspectAngle - differenceAngle;
    // 獲取行星速度
    double vel1 = position1.longitudeSpeed;
    double vel2 = position2.longitudeSpeed;

    // logger.d(
    //     '${position1.name}經度=${position1.longitude}, ${position2.name}經度=${position2.longitude}, 角度差=$differenceAngle, 相位角度=$aspectAngle, 容許度差=$orbis');
    // logger.d('${position1.name}速度=$vel1, ${position2.name}速度=$vel2');

    // 判斷是否為出相（separating）
    bool separating = false;

    // 根據 swisseph 算法判斷：
    // 如果 orbis < 0（角度小於精確角度）且 vel1 > vel2（第一個行星移動更快），則為出相
    if (orbis < 0 && vel1 > vel2) {
      separating = true;
    }
    // 如果 orbis > 0（角度大於精確角度）且 vel1 < vel2（第一個行星移動更慢），則為出相
    if (orbis > 0 && vel1 < vel2) {
      separating = true;
    }

    // 返回結果
    if (separating) {
      return AspectDirection.separating; // 出相
    } else {
      return AspectDirection.applying; // 入相
    }
  }

  /// 判斷行星互容接納狀態
  ///
  /// 檢查兩個行星之間是否存在互容或接納關係
  /// 互容：A行星進入B行星具有正向尊貴力量之處，不一定需要相位
  /// 接納：A行星進入B行星具有正向尊貴力量之處，且雙方有形成托勒密相位
  /// 返回互容接納類型和描述
  Map<String, dynamic> _checkReception(
      PlanetPosition planet1, PlanetPosition planet2,
      {bool hasAspect = false}) {
    // 預設無互容接納
    ReceptionType receptionType = ReceptionType.none;
    String? description;

    // 檢查行星1是否主管行星2所在星座
    bool planet1RulesPlanet2Sign =
        _isPlanetRulerOfSign(planet1.id, planet2.sign);

    // 檢查行星2是否主管行星1所在星座
    bool planet2RulesPlanet1Sign =
        _isPlanetRulerOfSign(planet2.id, planet1.sign);

    // 判斷互容接納類型
    if (planet1RulesPlanet2Sign && planet2RulesPlanet1Sign) {
      // 互相互容或互相接納：兩個行星都主管對方所在星座
      if (hasAspect) {
        // 如果有相位，則為互容接納
        receptionType = ReceptionType.mutualAcceptance;
        description =
            '${planet1.name}主管${planet2.sign}，${planet2.name}主管${planet1.sign}，且形成相位';
      } else {
        // 如果沒有相位，則為互容
        receptionType = ReceptionType.reception;
        description =
            '${planet1.name}主管${planet2.sign}，${planet2.name}主管${planet1.sign}';
      }
    } else if (planet1RulesPlanet2Sign) {
      // 單向互容或單向接納：行星1主管行星2所在星座
      if (hasAspect) {
        // 如果有相位，則為接納
        receptionType = ReceptionType.acceptance;
        description =
            '${planet1.name}主管${planet2.sign}，且形成相位\n${planet2.name}被${planet1.name}接納';
      } else {
        // 如果沒有相位，則無互容接納
        receptionType = ReceptionType.none;
        description = '${planet1.name}主管${planet2.sign}';
      }
    } else if (planet2RulesPlanet1Sign) {
      // 單向互容或單向接納：行星2主管行星1所在星座
      if (hasAspect) {
        // 如果有相位，則為接納
        receptionType = ReceptionType.acceptance;
        description =
            '${planet2.name}主管${planet1.sign}，且形成相位\n${planet1.name}被${planet2.name}接納';
      } else {
        // 如果沒有相位，則無互容接納
        receptionType = ReceptionType.none;
        description = '${planet2.name}主管${planet1.sign}';
      }
    }

    return {
      'receptionType': receptionType,
      'description': description,
    };
  }

  /// 簡化的宮位計算（假設上升點在牡羊座0度）
  int _calculateHouse(double longitude, {double ascendantDegree = 0.0}) {
    // 計算相對於上升點的位置
    double relativePosition = (longitude - ascendantDegree + 360) % 360;

    // 計算宮位（1-12）
    int house = ((relativePosition / 30) + 1).floor();

    // 確保宮位在1-12範圍內
    if (house <= 0) house += 12;
    if (house > 12) house %= 12;
    if (house == 0) house = 12;

    return house;
  }

  /// 根據經度計算宮位
  int _calculateHouseFromLongitude(
      double longitude, HouseCuspData? housesData) {
    // 遍歷宮位數據，找出行星所在的宮位
    for (int i = 1; i <= 12; i++) {
      final houseStart = housesData?.cusps[i];
      final houseEnd = housesData?.cusps[i + 1 > 12 ? 1 : i + 1] ?? (i * 30.0);

      // 處理跨越 0 度的宮位
      if (houseStart! > houseEnd) {
        if (longitude >= houseStart || longitude < houseEnd) {
          return i;
        }
      } else {
        if (longitude >= houseStart && longitude < houseEnd) {
          return i;
        }
      }
    }

    // 默認返回第一宮
    return 1;
  }

  /// 計算法達盤數據
  ///
  /// 本命盤是基本的星盤類型，顯示一個人出生時的行星位置
  Future<ChartData> _calculateFirdariaChart(
      ChartCalculationParams params) async {
    // 計算宮位
    HouseCuspData? houses = await calculateHouses(
        params.chartData.primaryPerson.dateTime,
        params.effectiveLatitude,
        params.effectiveLongitude,
        houseSystem: params.chartSettings?.houseSystem);

    // 計算行星位置
    final planets = await calculatePlanetPositions(
      params.chartData.primaryPerson.dateTime,
      housesData: houses,
      latitude: params.effectiveLatitude,
      longitude: params.effectiveLongitude,
      planetVisibility: params.planetVisibility,
    );
    _addSpecialPointsIfNeeded(
      positions: planets,
      housesData: houses,
      planetVisibility: params.planetVisibility,
    );
    // 使用獨立方法處理阿拉伯點計算
    final arabicPoints = await processArabicPoints(
      calculateArabicPoints: params.calculateArabicPoints,
      birthDate: params.chartData.primaryPerson.dateTime,
      planets: planets,
      houses: houses,
      latitude: params.effectiveLatitude,
      longitude: params.effectiveLongitude,
      planetVisibility: params.planetVisibility,
    );

    // 計算相位
    final aspects = calculateAspects(planets, aspectOrbs: params.aspectOrbs);

    // 計算行運盤的宮位
    final transitDate = params.chartData.specificDate ?? DateTime.now();

    // 返回計算結果
    return ChartData(
      chartType: params.chartData.chartType,
      primaryPerson: params.chartData.primaryPerson,
      specificDate: transitDate,
      planets: planets,
      houses: houses,
      aspects: aspects,
      arabicPoints: arabicPoints,
    );
  }

  /// 計算小限法數據
  ///
  /// 小限法使用本命盤的計算方法，因為小限法數據會在專門的頁面中計算
  Future<ChartData> _calculateProfectionChart(
      ChartCalculationParams params) async {
    logger.i('計算小限法數據');

    // 計算宮位
    HouseCuspData? houses = await calculateHouses(
        params.chartData.primaryPerson.dateTime,
        params.effectiveLatitude,
        params.effectiveLongitude,
        houseSystem: params.chartSettings?.houseSystem);

    // 計算行星位置
    final planets = await calculatePlanetPositions(
      params.chartData.primaryPerson.dateTime,
      housesData: houses,
      latitude: params.effectiveLatitude,
      longitude: params.effectiveLongitude,
      planetVisibility: params.planetVisibility,
    );
    _addSpecialPointsIfNeeded(
      positions: planets,
      housesData: houses,
      planetVisibility: params.planetVisibility,
    );

    // 使用獨立方法處理阿拉伯點計算
    final arabicPoints = await processArabicPoints(
      calculateArabicPoints: params.calculateArabicPoints,
      birthDate: params.chartData.primaryPerson.dateTime,
      planets: planets,
      houses: houses,
      latitude: params.effectiveLatitude,
      longitude: params.effectiveLongitude,
      planetVisibility: params.planetVisibility,
    );

    // 計算相位
    final aspects = calculateAspects(planets, aspectOrbs: params.aspectOrbs);

    // 計算推運時間
    final profectionDate = params.chartData.specificDate ?? DateTime.now();

    // 返回計算結果
    return ChartData(
      chartType: params.chartData.chartType,
      primaryPerson: params.chartData.primaryPerson,
      specificDate: profectionDate,
      planets: planets,
      houses: houses,
      aspects: aspects,
      arabicPoints: arabicPoints,
    );
  }

  /// 計算本命盤數據
  ///
  /// 本命盤是基本的星盤類型，顯示一個人出生時的行星位置
  Future<ChartData> _calculateNatalChart(ChartCalculationParams params) async {
    // 計算宮位
    HouseCuspData? houses = await calculateHouses(
      params.chartData.primaryPerson.dateTime,
      params.effectiveLatitude,
      params.effectiveLongitude,
      houseSystem: params.chartSettings?.houseSystem,
    );

    // 計算行星位置
    final planets = await calculatePlanetPositions(
      params.chartData.primaryPerson.dateTime,
      housesData: houses,
      latitude: params.effectiveLatitude,
      longitude: params.effectiveLongitude,
      planetVisibility: params.planetVisibility,
    );
    _addSpecialPointsIfNeeded(
      positions: planets,
      housesData: houses,
      planetVisibility: params.planetVisibility,
    );

    // 使用獨立方法處理阿拉伯點計算
    final arabicPoints = await processArabicPoints(
      calculateArabicPoints: params.calculateArabicPoints,
      birthDate: params.chartData.primaryPerson.dateTime,
      planets: planets,
      houses: houses,
      latitude: params.effectiveLatitude,
      longitude: params.effectiveLongitude,
      planetVisibility: params.planetVisibility,
    );

    // 計算相位
    final aspects = calculateAspects(planets, aspectOrbs: params.aspectOrbs);

    // 返回計算結果
    return ChartData(
      chartType: params.chartData.chartType,
      primaryPerson: params.chartData.primaryPerson,
      planets: planets,
      houses: houses,
      aspects: aspects,
      arabicPoints: arabicPoints,
    );
  }

  /// 計算行運盤數據
  ///
  /// 行運盤顯示當前天空的行星位置與本命盤的對比
  Future<ChartData> _calculateTransitChart(
      ChartCalculationParams params) async {
    logger.i('計算行運盤數據');

    // 計算本命盤的宮位
    final primaryHouses = await calculateHouses(
        params.chartData.primaryPerson.dateTime,
        params.effectiveLatitude,
        params.effectiveLongitude,
        houseSystem: params.chartSettings?.houseSystem);

    // 計算本命盤行星位置
    final primaryPlanets = await calculatePlanetPositions(
      params.chartData.primaryPerson.dateTime,
      housesData: primaryHouses,
      latitude: params.effectiveLatitude,
      longitude: params.effectiveLongitude,
      planetVisibility: params.planetVisibility,
    );

    _addSpecialPointsIfNeeded(
      positions: primaryPlanets,
      housesData: primaryHouses,
      planetVisibility: params.planetVisibility,
    );
    // 將本命盤行星位置存入主要人物屬性中
    params.chartData.primaryPerson.planets = primaryPlanets;

    // 計算行運盤的宮位
    final transitDate = params.chartData.specificDate ?? DateTime.now();

    final transitHouses = await calculateHouses(
        transitDate, params.effectiveLatitude, params.effectiveLongitude,
        houseSystem: params.chartSettings?.houseSystem);

    // 計算行運盤行星位置
    final transitPlanets = await calculatePlanetPositions(
      transitDate,
      housesData: primaryHouses,
      latitude: params.effectiveLatitude,
      longitude: params.effectiveLongitude,
      planetVisibility: params.planetVisibility,
    );

    _addSpecialPointsIfNeededDual(
      positions: transitPlanets,
      housesData: transitHouses,
      housesDataDual: primaryHouses!,
      planetVisibility: params.planetVisibility,
    );

    params.chartData.planets = transitPlanets; // 設置主要行星為第二個人的行星

    params.chartData.primaryPerson.aspects = calculateDualChartAspects(
        primaryPlanets, transitPlanets,
        aspectOrbs: params.aspectOrbs);

    // 計算相位
    params.chartData.aspects = params.chartData.primaryPerson.aspects;

    // 返回計算結果
    return ChartData(
      chartType: params.chartData.chartType,
      primaryPerson: params.chartData.primaryPerson,
      secondaryPerson: params.chartData.secondaryPerson,
      specificDate: transitDate,
      planets: transitPlanets,
      houses: primaryHouses,
      aspects: params.chartData.aspects,
    );
  }

  /// 計算三限推運盤數據
  ///
  /// 三限推運盤是一種預測性星盤，一天代表一個月，用於分析短期心理變化
  Future<ChartData> _calculateTertiaryProgressionChart(
      ChartCalculationParams params) async {
    logger.i('計算三限推運盤數據');

    // 確保有特定日期
    if (params.chartData.specificDate == null) {
      throw Exception('三限推運盤需要指定目標日期');
    }

    logger.d('出生日期: ${params.chartData.primaryPerson.dateTime}');
    logger.d('目標日期: ${params.chartData.specificDate}');

    // 計算三限推運日期
    final tertiaryDate = AstrologyCalculator.calculateTertiaryProgressionDate(
      params.chartData.primaryPerson.dateTime,
      params.chartData.specificDate!,
    );
    logger.d('三限推運日期: $tertiaryDate');

    // 計算宮位
    HouseCuspData? houses = await calculateHouses(
        tertiaryDate, params.effectiveLatitude, params.effectiveLongitude,
        houseSystem: params.chartSettings?.houseSystem);
    logger.d('三限推運宮位計算完成');

    if (houses != null) {
      logger.d('上升點經度: ${houses.ascmc[0]}');
      logger.d('中天經度: ${houses.ascmc[1]}');
    }

    // 計算行星位置
    logger.d('開始計算三限推運行星位置');
    final planets = await calculatePlanetPositions(
      tertiaryDate,
      housesData: houses,
      latitude: params.effectiveLatitude,
      longitude: params.effectiveLongitude,
      planetVisibility: params.planetVisibility,
    );

    _addSpecialPointsIfNeeded(
      positions: planets,
      housesData: houses,
      planetVisibility: params.planetVisibility,
    );

    logger.d('三限推運行星位置計算完成');

    // 記錄太陽和月亮的位置，用於調試
    for (final planet in planets) {
      if (planet.name == '太陽') {
        logger.d(
            '太陽位置: 經度=${planet.longitude.toStringAsFixed(2)}, 星座=${planet.sign}, 宮位=${planet.house}');
      } else if (planet.name == '月亮') {
        logger.d(
            '月亮位置: 經度=${planet.longitude.toStringAsFixed(2)}, 星座=${planet.sign}, 宮位=${planet.house}');
      }
    }

    // 計算相位
    logger.d('開始計算三限推運相位');
    final aspects = calculateAspects(planets, aspectOrbs: params.aspectOrbs);
    logger.d('三限推運相位計算完成');
    logger.d('相位數量: ${aspects.length}');

    // 返回計算結果
    return ChartData(
      chartType: params.chartData.chartType,
      primaryPerson: params.chartData.primaryPerson,
      specificDate: params.chartData.specificDate,
      planets: planets,
      houses: houses,
      aspects: aspects,
    );
  }

  /// 計算太陽弧推運盤數據
  ///
  /// 太陽弧推運盤是一種預測性星盤，所有行星以太陽推進度數前進，用於觀察重大人生變化
  Future<ChartData> _calculateSolarArcDirectionChart(
      ChartCalculationParams params) async {
    logger.i('計算太陽弧推運盤數據');

    // 確保有特定日期
    if (params.chartData.specificDate == null) {
      throw Exception('太陽弧推運盤需要指定目標日期');
    }

    logger.d('出生日期: ${params.chartData.primaryPerson.dateTime}');
    logger.d('目標日期: ${params.chartData.specificDate}');

    // 計算本命盤的宮位
    HouseCuspData? houses = await calculateHouses(
        params.chartData.primaryPerson.dateTime,
        params.effectiveLatitude,
        params.effectiveLongitude,
        houseSystem: params.chartSettings?.houseSystem);

    // 計算太陽弧推運度數
    final solarArcDegrees = await AstrologyCalculator.calculateSolarArcDegrees(
      params.chartData.primaryPerson,
      params.chartData.specificDate!,
    );
    logger.d('太陽弧推運度數: $solarArcDegrees');

    // 計算目標日期的宮位
    HouseCuspData? solarArcHouses = await calculateHouses(
        params.chartData.specificDate!,
        params.effectiveLatitude,
        params.effectiveLongitude,
        houseSystem: params.chartSettings?.houseSystem);
    logger.d('太陽弧推運宮位計算完成');

    // 計算出生時的行星位置
    final birthPlanets = await calculatePlanetPositions(
      params.chartData.primaryPerson.dateTime,
      housesData: houses,
      latitude: params.effectiveLatitude,
      longitude: params.effectiveLongitude,
      planetVisibility: params.planetVisibility,
    );

    _addSpecialPointsIfNeeded(
      positions: birthPlanets,
      housesData: houses,
      planetVisibility: params.planetVisibility,
    );

    logger.d('出生時行星位置計算完成');

    // 將出生時行星位置存入主要人物屬性中
    params.chartData.primaryPerson.planets = birthPlanets;

    // 將每個行星的經度增加太陽弧推運度數
    final List<PlanetPosition> solarArcPlanets = [];
    for (final planet in birthPlanets) {
      // 計算新的經度（加上太陽弧推運度數）
      double newLongitude = (planet.longitude + solarArcDegrees) % 360;

      // 計算新的星座
      final newSign = _getZodiacSign(newLongitude);

      // 計算新的宮位
      int newHouse = 0;
      if (solarArcHouses != null) {
        newHouse = AstrologyCalculator.calculateHouseFromLongitude(
          newLongitude,
          houses!,
        );
      }

      // 創建新的行星位置對象
      final newPlanet = PlanetPosition(
        id: planet.id,
        name: planet.name,
        symbol: planet.symbol,
        color: planet.color,
        longitude: newLongitude,
        latitude: planet.latitude,
        distance: planet.distance,
        longitudeSpeed: planet.longitudeSpeed,
        latitudeSpeed: planet.latitudeSpeed,
        distanceSpeed: planet.distanceSpeed,
        sign: newSign,
        house: newHouse,
        dignity: planet.dignity,
        solarCondition: planet.solarCondition,
        isDaytime: planet.isDaytime,
        houseType: planet.houseType,
        isPlanetDiurnal: planet.isPlanetDiurnal,
        isSignMasculine: planet.isSignMasculine,
        sectStatus: planet.sectStatus,
      );

      solarArcPlanets.add(newPlanet);

      // 記錄太陽和月亮的新位置，用於調試
      if (planet.name == '太陽' || planet.name == '月亮') {
        logger.d(
            '${planet.name}原始位置: 經度=${planet.longitude.toStringAsFixed(2)}, 星座=${planet.sign}, 宮位=${planet.house}');
        logger.d(
            '${planet.name}新位置: 經度=${newLongitude.toStringAsFixed(2)}, 星座=$newSign, 宮位=$newHouse');
      }
    }

    // 計算相位
    logger.d('開始計算太陽弧推運相位');
    final aspects =
        calculateAspects(solarArcPlanets, aspectOrbs: params.aspectOrbs);
    logger.d('太陽弧推運相位計算完成');
    logger.d('相位數量: ${aspects.length}');

    // 使用獨立方法處理阿拉伯點計算
    final arabicPoints = await processArabicPoints(
      calculateArabicPoints: params.calculateArabicPoints,
      birthDate: params.chartData.primaryPerson.dateTime,
      planets: solarArcPlanets,
      houses: houses,
      latitude: params.effectiveLatitude,
      longitude: params.effectiveLongitude,
      planetVisibility: params.planetVisibility,
    );

    // 返回計算結果
    return ChartData(
      chartType: params.chartData.chartType,
      primaryPerson: params.chartData.primaryPerson,
      specificDate: params.chartData.specificDate,
      planets: solarArcPlanets,
      houses: houses,
      aspects: aspects,
    );
  }

  /// 計算太陽返照盤數據
  ///
  /// 太陽返照盤是太陽回到出生位置時的星盤，用於年度運勢分析
  Future<ChartData> _calculateSolarReturnChart(
      ChartCalculationParams params) async {
    logger.i('計算太陽返照盤數據');

    // 確保有特定日期
    if (params.chartData.specificDate == null) {
      throw Exception('太陽返照盤需要指定目標日期');
    }

    logger.d('出生日期: ${params.chartData.primaryPerson.dateTime}');
    logger.d('目標日期: ${params.chartData.specificDate}');

    // 首先需要計算出生時太陽的經度
    final birthJulianDay = await JulianDateUtils.dateTimeToJulianDay(
      params.chartData.primaryPerson.dateTime,
      params.chartData.primaryPerson.latitude,
      params.chartData.primaryPerson.longitude,
    );

    CoordinatesWithSpeed birthSunResult =
        sweCalcUt(birthJulianDay, HeavenlyBody.SE_SUN);

    final birthSunLongitude = birthSunResult.longitude;
    logger.d('出生時太陽經度: $birthSunLongitude');

    final birthday = DateTime(
      params.chartData.primaryPerson.dateTime.year,
      params.chartData.primaryPerson.dateTime.month,
      params.chartData.primaryPerson.dateTime.day,
    );

    final currentDate = params.chartData.specificDate!;
    // 計算太陽返照日期
    final targetYear = (currentDate.isBefore(DateTime(
      currentDate.year,
      birthday.month,
      birthday.day,
    )))
        ? currentDate.year - 1
        : currentDate.year;

    DateTime solarReturnDate;
    try {
      solarReturnDate = await AstrologyCalculator.calculateSolarReturnDate(
        params.chartData.primaryPerson,
        targetYear,
        birthSunLongitude,
      );
      logger.d('太陽返照日期: $solarReturnDate');
    } catch (e) {
      // 如果計算太陽返照日期出錯，使用目標年份的生日
      logger.e('計算太陽返照日期出錯: $e');
      solarReturnDate = DateTime(
        targetYear,
        params.chartData.primaryPerson.dateTime.month,
        params.chartData.primaryPerson.dateTime.day,
        params.chartData.primaryPerson.dateTime.hour,
        params.chartData.primaryPerson.dateTime.minute,
        params.chartData.primaryPerson.dateTime.second,
      );
      logger.d('使用備用太陽返照日期: $solarReturnDate');
    }

    // 計算太陽返照盤的宮位
    final houses = await calculateHouses(
        solarReturnDate, params.effectiveLatitude, params.effectiveLongitude,
        houseSystem: params.chartSettings?.houseSystem);
    logger.d('太陽返照盤宮位計算完成');

    if (houses != null) {
      logger.d('上升點經度: ${houses.ascmc[0]}');
      logger.d('中天經度: ${houses.ascmc[1]}');
    }

    // 首先計算本命盤行星位置（用於比較模式）
    logger.d('開始計算本命盤行星位置');
    final natalPlanets = await calculatePlanetPositions(
      params.chartData.primaryPerson.dateTime,
      housesData: houses, // 使用返照盤的宮位
      latitude: params.effectiveLatitude,
      longitude: params.effectiveLongitude,
      planetVisibility: params.planetVisibility,
    );

    _addSpecialPointsIfNeeded(
      positions: natalPlanets,
      housesData: houses,
      planetVisibility: params.planetVisibility,
    );

    // 將本命盤行星數據保存到 primaryPerson 中
    params.chartData.primaryPerson.planets = natalPlanets;
    logger.d('本命盤行星位置計算完成');

    // 計算太陽返照盤的行星位置
    logger.d('開始計算太陽返照盤行星位置');
    final planets = await calculatePlanetPositions(
      solarReturnDate,
      housesData: houses,
      latitude: params.effectiveLatitude,
      longitude: params.effectiveLongitude,
      planetVisibility: params.planetVisibility,
    );

    _addSpecialPointsIfNeeded(
      positions: planets,
      housesData: houses,
      planetVisibility: params.planetVisibility,
    );

    logger.d('太陽返照盤行星位置計算完成');

    // 記錄太陽和月亮的位置，用於調試
    for (final planet in planets) {
      if (planet.name == '太陽') {
        logger.d(
            '太陽位置: 經度=${planet.longitude.toStringAsFixed(2)}, 星座=${planet.sign}, 宮位=${planet.house}');
      } else if (planet.name == '月亮') {
        logger.d(
            '月亮位置: 經度=${planet.longitude.toStringAsFixed(2)}, 星座=${planet.sign}, 宮位=${planet.house}');
      }
    }

    // 計算相位
    logger.d('開始計算太陽返照盤相位');
    final aspects = calculateAspects(planets, aspectOrbs: params.aspectOrbs);
    logger.d('太陽返照盤相位計算完成');
    logger.d('相位數量: ${aspects.length}');

    // 使用獨立方法處理阿拉伯點計算
    final arabicPoints = await processArabicPoints(
      calculateArabicPoints: params.calculateArabicPoints,
      birthDate: params.chartData.primaryPerson.dateTime,
      planets: planets,
      houses: houses,
      latitude: params.effectiveLatitude,
      longitude: params.effectiveLongitude,
      planetVisibility: params.planetVisibility,
    );
    // 返回計算結果
    return ChartData(
      chartType: params.chartData.chartType,
      primaryPerson: params.chartData.primaryPerson,
      specificDate: params.chartData.specificDate,
      // 保持用戶原本設定的目標日期
      returnDate: solarReturnDate,
      // 實際計算出的太陽返照日期
      planets: planets,
      houses: houses,
      aspects: aspects,
    );
  }

  /// 計算月亮返照盤數據
  ///
  /// 月亮返照盤：月亮回到出生位置時的星盤，用於月度運勢分析
  Future<ChartData> _calculateLunarReturnChart(
      ChartCalculationParams params) async {
    logger.i('計算月亮返照盤數據');

    // 確保有特定日期
    if (params.chartData.specificDate == null) {
      throw Exception('月亮返照盤需要指定目標日期');
    }

    logger.d('出生日期: ${params.chartData.primaryPerson.dateTime}');
    logger.d('目標日期: ${params.chartData.specificDate}');

    // 首先需要計算出生時太陽的經度
    final birthJulianDay = await JulianDateUtils.dateTimeToJulianDay(
      params.chartData.primaryPerson.dateTime,
      params.chartData.primaryPerson.latitude,
      params.chartData.primaryPerson.longitude,
    );

    CoordinatesWithSpeed birthMoonResult =
        sweCalcUt(birthJulianDay, HeavenlyBody.SE_MOON);

    final birthMoonLongitude = birthMoonResult.longitude;
    logger.d('出生時月亮經度: $birthMoonLongitude');

    // 計算月亮返照日期
    DateTime lunarReturnDate;
    try {
      lunarReturnDate = await AstrologyCalculator.calculateLunarReturnDate(
        params.chartData.primaryPerson,
        params.chartData.specificDate!,
        birthMoonLongitude,
      );
      logger.d('月亮返照日期: $lunarReturnDate');
    } catch (e) {
      // 如果計算月亮返照日期出錯，使用目標日期
      logger.e('計算月亮返照日期出錯: $e');
      lunarReturnDate = params.chartData.specificDate!;
      logger.d('使用備用月亮返照日期: $lunarReturnDate');
    }

    // 計算月亮返照盤的宮位
    final lunarReturnHouses = await calculateHouses(
        lunarReturnDate,
        params.chartData.primaryPerson.latitude,
        params.chartData.primaryPerson.longitude,
        houseSystem: params.chartSettings?.houseSystem);
    logger.d('月亮返照盤宮位計算完成');
    if (lunarReturnHouses != null) {
      logger.d('上升點經度: ${lunarReturnHouses.ascmc[0]}');
      logger.d('中天經度: ${lunarReturnHouses.ascmc[1]}');
    }

    // 首先計算本命盤行星位置（用於比較模式）
    logger.d('開始計算本命盤行星位置');
    final natalPlanets = await calculatePlanetPositions(
      params.chartData.primaryPerson.dateTime,
      housesData: lunarReturnHouses, // 使用返照盤的宮位
      latitude: params.chartData.primaryPerson.latitude,
      longitude: params.chartData.primaryPerson.longitude,
      planetVisibility: params.planetVisibility,
    );

    _addSpecialPointsIfNeeded(
      positions: natalPlanets,
      housesData: lunarReturnHouses,
      planetVisibility: params.planetVisibility,
    );

    // 將本命盤行星數據保存到 primaryPerson 中
    params.chartData.primaryPerson.planets = natalPlanets;
    logger.d('本命盤行星位置計算完成');

    // 計算月亮返照盤的行星位置
    logger.d('開始計算月亮返照盤行星位置');
    params.chartData.planets = await calculatePlanetPositions(
      lunarReturnDate,
      housesData: lunarReturnHouses,
      latitude: params.chartData.primaryPerson.latitude,
      longitude: params.chartData.primaryPerson.longitude,
      planetVisibility: params.planetVisibility,
    );

    _addSpecialPointsIfNeeded(
      positions: params.chartData.planets!,
      housesData: lunarReturnHouses,
      planetVisibility: params.planetVisibility,
    );

    logger.d('月亮返照盤行星位置計算完成');

    // 記錄太陽和月亮的位置，用於調試
    if (params.chartData.planets != null) {
      for (final planet in params.chartData.planets!) {
        if (planet.name == '太陽') {
          logger.d(
              '太陽位置: 經度=${planet.longitude.toStringAsFixed(2)}, 星座=${planet.sign}, 宮位=${planet.house}');
        } else if (planet.name == '月亮') {
          logger.d(
              '月亮位置: 經度=${planet.longitude.toStringAsFixed(2)}, 星座=${planet.sign}, 宮位=${planet.house}');
        }
      }
    }

    // 計算相位
    logger.d('開始計算月亮返照盤相位');
    final lunarReturnAspects = calculateAspects(params.chartData.planets!,
        aspectOrbs: params.aspectOrbs);
    logger.d('月亮返照盤相位計算完成');
    logger.d('相位數量: ${lunarReturnAspects.length}');

    return ChartData(
      chartType: params.chartData.chartType,
      primaryPerson: params.chartData.primaryPerson,
      specificDate: params.chartData.specificDate,
      // 保持用戶原本設定的目標日期
      returnDate: lunarReturnDate,
      // 實際計算出的月亮返照日期
      planets: params.chartData.planets,
      houses: lunarReturnHouses,
      aspects: lunarReturnAspects,
    );
  }

  /// 計算比較次限盤數據
  ///
  /// 比較次限盤是使用第二個人的次限推運盤計算行星位置，但使用第一個人的宮位
  Future<ChartData> _calculateSynastrySecondaryChart(
      ChartCalculationParams params) async {
    logger.i('計算比較次限盤數據');

    // 確保有第二個人的數據
    if (params.chartData.secondaryPerson == null) {
      throw Exception('比較次限盤需要兩個人的數據');
    }

    // 確保有特定日期
    if (params.chartData.specificDate == null) {
      throw Exception('比較次限盤需要指定目標日期');
    }

    params.chartData.primaryPerson = await generateSecondaryProgressedChart(
      targetDate: params.chartData.specificDate!,
      person: params.chartData.primaryPerson,
      params: params,
      chartType: params.chartData.chartType,
    );

    params.chartData.secondaryPerson = await generateSecondaryProgressedChart(
      targetDate: params.chartData.specificDate!,
      person: params.chartData.secondaryPerson!,
      params: params,
      chartType: params.chartData.chartType,
    );

    params.chartData.planets = params.chartData.primaryPerson.planets!;
    params.chartData.houses = params.chartData.primaryPerson.houses!;
    params.chartData.aspects = params.chartData.primaryPerson.aspects!;
    return params.chartData;
  }

  /// 計算比較三限盤數據
  ///
  /// 比較三限盤是分別計算兩個人的三限推運盤，然後將這兩個三限推運盤進行比較
  Future<ChartData> _calculateSynastryTertiaryChart(
      ChartCalculationParams params) async {
    logger.i('計算比較三限盤數據');

    // 確保有第二個人的數據
    if (params.chartData.secondaryPerson == null) {
      throw Exception('比較三限盤需要兩個人的數據');
    }

    // 確保有特定日期
    if (params.chartData.specificDate == null) {
      throw Exception('比較三限盤需要指定目標日期');
    }

    params.chartData.primaryPerson = await generateTertiaryProgressedChart(
      birthDate: params.chartData.primaryPerson.dateTime,
      targetDate: params.chartData.specificDate!,
      person: params.chartData.primaryPerson,
      latitude: params.chartData.primaryPerson.latitude,
      longitude: params.chartData.primaryPerson.longitude,
      params: params,
      chartType: params.chartData.chartType,
    );

    params.chartData.secondaryPerson = await generateTertiaryProgressedChart(
      birthDate: params.chartData.secondaryPerson!.dateTime,
      targetDate: params.chartData.specificDate!,
      person: params.chartData.secondaryPerson!,
      latitude: params.chartData.secondaryPerson!.latitude,
      longitude: params.chartData.secondaryPerson!.longitude,
      params: params,
      chartType: params.chartData.chartType,
    );

    params.chartData.planets = params.chartData.primaryPerson.planets!;
    params.chartData.houses = params.chartData.primaryPerson.houses!;
    params.chartData.aspects = params.chartData.primaryPerson.aspects!;

    return params.chartData;
  }

  /// 計算組合盤數據
  ///
  /// 組合盤是將兩人星盤中的行星位置取中點，產生一個新的星盤，代表兩人關係的本質
  Future<ChartData> _calculateCompositeChart(
      ChartCalculationParams params) async {
    logger.i('計算組合盤數據');

    final personA = params.chartData.primaryPerson;
    final personB = params.chartData.secondaryPerson;
    if (personB == null) {
      throw Exception('組合盤需要兩個人的數據');
    }

    // 計算兩人宮位
    final housesA = await calculateHouses(
        personA.dateTime, personA.latitude, personA.longitude,
        houseSystem: params.chartSettings?.houseSystem);
    final housesB = await calculateHouses(
        personB.dateTime, personB.latitude, personB.longitude,
        houseSystem: params.chartSettings?.houseSystem);

    // 計算行星位置
    final planetsA = await calculatePlanetPositions(
      personA.dateTime,
      latitude: personA.latitude,
      longitude: personA.longitude,
      housesData: housesA,
      planetVisibility: params.planetVisibility,
    );

    _addSpecialPointsIfNeeded(
      positions: planetsA,
      housesData: housesA,
      planetVisibility: params.planetVisibility,
    );

    final planetsB = await calculatePlanetPositions(
      personB.dateTime,
      latitude: personB.latitude,
      longitude: personB.longitude,
      housesData: housesB,
      planetVisibility: params.planetVisibility,
    );

    _addSpecialPointsIfNeeded(
      positions: planetsB,
      housesData: housesB,
      planetVisibility: params.planetVisibility,
    );

    // 計算組合盤的行星位置（使用角度中點邏輯）
    final compositePlanets = <PlanetPosition>[];
    for (int i = 0; i < planetsA.length; i++) {
      final a = planetsA[i];
      final b = planetsB.firstWhere((p) => p.id == a.id);

      final midLongitude = _midpointAngle(a.longitude, b.longitude);
      final midLatitude = (a.latitude + b.latitude) / 2;
      final midSign = _getZodiacSign(midLongitude);

      final compositePlanet = a.copyWith(
        longitude: midLongitude,
        latitude: midLatitude,
        sign: midSign,
      );
      final PlanetDignity dignity = _calculatePlanetDignity(
          compositePlanet.id, midSign,
          longitude: midLongitude, isDaytime: a.isDaytime);

      compositePlanet.dignity = dignity;
      compositePlanets.add(compositePlanet);
    }

    // 組合宮位中點（含 ASC/MC）
    final compositeCusps = List<double>.filled(13, 0.0);
    for (int i = 1; i <= 12; i++) {
      compositeCusps[i] = _midpointAngle(housesA!.cusps[i], housesB!.cusps[i]);
    }

    final compositeAscmc = List<double>.filled(10, 0.0);
    for (int i = 1; i < housesA!.ascmc.length; i++) {
      compositeAscmc[i] = _midpointAngle(housesA.ascmc[i], housesB!.ascmc[i]);
    }

    // 建立組合盤宮位（模擬 Sweph 資料結構）
    final compositeHouses = HouseCuspData(compositeCusps, compositeAscmc);
    for (final planet in compositePlanets) {
      planet.house = AstrologyCalculator.calculateHouseFromLongitude(
        planet.longitude,
        compositeHouses,
      );
    }

    // 計算相位
    final aspects =
        calculateAspects(compositePlanets, aspectOrbs: params.aspectOrbs);

    // 回傳組合盤資料
    return ChartData(
      chartType: ChartType.composite,
      primaryPerson: personA,
      secondaryPerson: personB,
      planets: compositePlanets,
      houses: compositeHouses,
      aspects: aspects,
    );
  }

  Future<HouseCuspData?> calculateSecondaryProgressedHouses(
      DateTime birthDate,
      DateTime specificDate,
      double latitude,
      double longitude,
      ChartCalculationParams params) async {
    // 計算次限推運日期（可用於日曆顯示）
    final secondaryDate = AstrologyCalculator.calculateSecondaryProgressionDate(
      birthDate,
      specificDate,
    );
    logger.d('次限推運日期: $secondaryDate');

    // 計算推進天數（1天 = 1年）
    final progressedDays =
        AstrologyCalculator.calculateSecondaryProgressionYears(
      birthDate,
      specificDate,
    );

    // 計算推進後的實際日期
    final progressedDate = birthDate.add(Duration(days: progressedDays));

    // 計算宮位
    final houses = await calculateHouses(progressedDate, latitude, longitude,
        houseSystem: params.chartSettings?.houseSystem);
    logger.d('次限推運宮位計算完成');

    return houses;
  }

  /// 計算組合次限盤數據
  ///
  /// 組合次限盤是先分別計算兩個人的次限推運盤，然後將這兩個次限推運盤進行組合計算
  Future<ChartData> _calculateCompositeSecondaryChart(
      ChartCalculationParams params) async {
    logger.i('計算組合次限盤數據');

    // 確保有第二個人的數據
    if (params.chartData.secondaryPerson == null) {
      throw Exception('組合次限盤需要兩個人的數據');
    }

    // 確保有特定日期
    if (params.chartData.specificDate == null) {
      throw Exception('組合次限盤需要指定目標日期');
    }

    final personA = params.chartData.primaryPerson;
    final personB = params.chartData.secondaryPerson!;

    // 1. 計算第一個人的次限推運日期
    final secondaryDateA =
        AstrologyCalculator.calculateSecondaryProgressionDate(
      personA.dateTime,
      params.chartData.specificDate!,
    );
    logger.d('第一個人的次限推運日期: $secondaryDateA');

    // 2. 計算第二個人的次限推運日期
    final secondaryDateB =
        AstrologyCalculator.calculateSecondaryProgressionDate(
      personB.dateTime,
      params.chartData.specificDate!,
    );
    logger.d('第二個人的次限推運日期: $secondaryDateB');

    HouseCuspData? houseCuspDataA = await calculateSecondaryProgressedHouses(
        personA.dateTime,
        params.chartData.specificDate!,
        personA.latitude,
        personA.longitude,
        params);

    HouseCuspData? houseCuspDataB = await calculateSecondaryProgressedHouses(
        personB.dateTime,
        params.chartData.specificDate!,
        personB.latitude,
        personB.longitude,
        params);

    // 5. 計算第一個人的次限推運盤行星位置
    final planetsA = await calculatePlanetPositions(
      secondaryDateA,
      latitude: personA.latitude,
      longitude: personA.longitude,
      housesData: houseCuspDataA,
      planetVisibility: params.planetVisibility,
    );

    _addSpecialPointsIfNeeded(
      positions: planetsA,
      housesData: houseCuspDataA,
      planetVisibility: params.planetVisibility,
    );

    // 6. 計算第二個人的次限推運盤行星位置
    final planetsB = await calculatePlanetPositions(
      secondaryDateB,
      latitude: personB.latitude,
      longitude: personB.longitude,
      housesData: houseCuspDataB,
      planetVisibility: params.planetVisibility,
    );

    _addSpecialPointsIfNeeded(
      positions: planetsB,
      housesData: houseCuspDataB,
      planetVisibility: params.planetVisibility,
    );

    // 7. 計算組合盤的行星位置（使用角度中點邏輯）
    final compositePlanets = <PlanetPosition>[];
    for (int i = 0; i < planetsA.length; i++) {
      final a = planetsA[i];
      final b = planetsB.firstWhere((p) => p.id == a.id);

      final midLongitude = _midpointAngle(a.longitude, b.longitude);
      final midLatitude = (a.latitude + b.latitude) / 2;
      final midSign = _getZodiacSign(midLongitude);

      final compositePlanet = a.copyWith(
        longitude: midLongitude,
        latitude: midLatitude,
        sign: midSign,
      );
      final PlanetDignity dignity = _calculatePlanetDignity(
          compositePlanet.id, midSign,
          longitude: midLongitude, isDaytime: a.isDaytime);

      compositePlanet.dignity = dignity;
      compositePlanets.add(compositePlanet);
    }

    // 8. 組合宮位中點（含 ASC/MC）
    final compositeCusps = List<double>.filled(13, 0.0);
    for (int i = 1; i <= 12; i++) {
      compositeCusps[i] =
          _midpointAngle(houseCuspDataA!.cusps[i], houseCuspDataB!.cusps[i]);
    }

    final compositeAscmc = List<double>.filled(10, 0.0);
    for (int i = 1; i < houseCuspDataA!.ascmc.length; i++) {
      compositeAscmc[i] =
          _midpointAngle(houseCuspDataA.ascmc[i], houseCuspDataB!.ascmc[i]);
    }

    // 9. 建立組合盤宮位（模擬 Sweph 資料結構）
    final compositeHouses = HouseCuspData(compositeCusps, compositeAscmc);
    for (final planet in compositePlanets) {
      planet.house = AstrologyCalculator.calculateHouseFromLongitude(
        planet.longitude,
        compositeHouses,
      );
    }

    // 10. 計算相位
    final aspects =
        calculateAspects(compositePlanets, aspectOrbs: params.aspectOrbs);

    // 回傳組合次限盤資料
    return ChartData(
      chartType: ChartType.compositeSecondary,
      primaryPerson: personA,
      secondaryPerson: personB,
      planets: compositePlanets,
      houses: compositeHouses,
      aspects: aspects,
      specificDate: params.chartData.specificDate,
    );
  }

  Future<HouseCuspData?> calculateTertiaryProgressedHouses(
      DateTime birthDate,
      DateTime specificDate,
      double latitude,
      double longitude,
      ChartCalculationParams params) async {
    // 計算三限推運日期（1日 = 1月）
    final tertiaryDate = AstrologyCalculator.calculateTertiaryProgressionDate(
      birthDate,
      specificDate,
    );
    logger.d('三限推運日期: $tertiaryDate');

    // 計算宮位
    final houses = await calculateHouses(tertiaryDate, latitude, longitude,
        houseSystem: params.chartSettings?.houseSystem);
    logger.d('三限推運宮位計算完成');

    return houses;
  }

  /// 計算組合三限盤數據
  ///
  /// 組合三限盤是先分別計算兩個人的三限推運盤，然後將這兩個三限推運盤進行組合計算
  Future<ChartData> _calculateCompositeTertiaryChart(
      ChartCalculationParams params) async {
    logger.i('計算組合三限盤數據');

    // 確保有第二個人的數據
    if (params.chartData.secondaryPerson == null) {
      throw Exception('組合三限盤需要兩個人的數據');
    }

    // 確保有特定日期
    if (params.chartData.specificDate == null) {
      throw Exception('組合三限盤需要指定目標日期');
    }

    final personA = params.chartData.primaryPerson;
    final personB = params.chartData.secondaryPerson!;

    // 1. 計算第一個人的三限推運日期
    final tertiaryDateA = AstrologyCalculator.calculateTertiaryProgressionDate(
      personA.dateTime,
      params.chartData.specificDate!,
    );
    logger.d('第一個人的三限推運日期: $tertiaryDateA');

    // 2. 計算第二個人的三限推運日期
    final tertiaryDateB = AstrologyCalculator.calculateTertiaryProgressionDate(
      personB.dateTime,
      params.chartData.specificDate!,
    );
    logger.d('第二個人的三限推運日期: $tertiaryDateB');

    // 3. 計算第一個人的三限推運盤宮位
    HouseCuspData? houseCuspDataA = await calculateTertiaryProgressedHouses(
        personA.dateTime,
        params.chartData.specificDate!,
        personA.latitude,
        personA.longitude,
        params);

    // 4. 計算第二個人的三限推運盤宮位
    HouseCuspData? houseCuspDataB = await calculateTertiaryProgressedHouses(
        personB.dateTime,
        params.chartData.specificDate!,
        personB.latitude,
        personB.longitude,
        params);

    // 5. 計算第一個人的三限推運盤行星位置
    final planetsA = await calculatePlanetPositions(
      tertiaryDateA,
      latitude: personA.latitude,
      longitude: personA.longitude,
      housesData: houseCuspDataA,
      planetVisibility: params.planetVisibility,
    );

    _addSpecialPointsIfNeeded(
      positions: planetsA,
      housesData: houseCuspDataA,
      planetVisibility: params.planetVisibility,
    );

    // 6. 計算第二個人的三限推運盤行星位置
    final planetsB = await calculatePlanetPositions(
      tertiaryDateB,
      latitude: personB.latitude,
      longitude: personB.longitude,
      housesData: houseCuspDataB,
      planetVisibility: params.planetVisibility,
    );

    _addSpecialPointsIfNeeded(
      positions: planetsB,
      housesData: houseCuspDataB,
      planetVisibility: params.planetVisibility,
    );

    // 7. 計算組合盤的行星位置（使用角度中點邏輯）
    final compositePlanets = <PlanetPosition>[];
    for (int i = 0; i < planetsA.length; i++) {
      final a = planetsA[i];
      final b = planetsB.firstWhere((p) => p.id == a.id);

      final midLongitude = _midpointAngle(a.longitude, b.longitude);
      final midLatitude = (a.latitude + b.latitude) / 2;
      final midSign = _getZodiacSign(midLongitude);

      final compositePlanet = a.copyWith(
        longitude: midLongitude,
        latitude: midLatitude,
        sign: midSign,
      );

      final PlanetDignity dignity = _calculatePlanetDignity(
          compositePlanet.id, midSign,
          longitude: midLongitude, isDaytime: a.isDaytime);

      compositePlanet.dignity = dignity;

      compositePlanets.add(compositePlanet);
    }

    // 8. 組合宮位中點（含 ASC/MC）
    final compositeCusps = List<double>.filled(13, 0.0);
    for (int i = 1; i <= 12; i++) {
      compositeCusps[i] =
          _midpointAngle(houseCuspDataA!.cusps[i], houseCuspDataB!.cusps[i]);
    }

    final compositeAscmc = List<double>.filled(10, 0.0);
    for (int i = 0;
        i < houseCuspDataA!.ascmc.length && i < houseCuspDataB!.ascmc.length;
        i++) {
      compositeAscmc[i] =
          _midpointAngle(houseCuspDataA.ascmc[i], houseCuspDataB.ascmc[i]);
    }

    // 9. 建立組合盤宮位（模擬 Sweph 資料結構）
    final compositeHouses = HouseCuspData(compositeCusps, compositeAscmc);
    for (final planet in compositePlanets) {
      planet.house = AstrologyCalculator.calculateHouseFromLongitude(
        planet.longitude,
        compositeHouses,
      );
    }

    // 10. 計算相位
    final aspects =
        calculateAspects(compositePlanets, aspectOrbs: params.aspectOrbs);

    // 回傳組合三限盤資料
    return ChartData(
      chartType: ChartType.compositeTertiary,
      primaryPerson: personA,
      secondaryPerson: personB,
      planets: compositePlanets,
      houses: compositeHouses,
      aspects: aspects,
      specificDate: params.chartData.specificDate,
    );
  }

  double _midpointAngle(double angleA, double angleB) {
    var a = angleA;
    var b = angleB;

    if ((a - b).abs() > 180) {
      if (a < b) {
        a += 360;
      } else {
        b += 360;
      }
    }

    var mid = (a + b) / 2;
    if (mid >= 360) mid -= 360;
    return mid;
  }

  /// 計算時空中點盤數據
  ///
  /// 時空中點盤是以兩人生日取算術平均，推算新時間與地點建立星盤
  Future<ChartData> _calculateDavisonChart(
      ChartCalculationParams params) async {
    logger.i('計算時空中點盤數據');

    final personA = params.chartData.primaryPerson;
    final personB = params.chartData.secondaryPerson;
    if (personB == null) {
      throw Exception('時空中點盤需要兩個人的數據');
    }

    // 計算兩人出生時間的中點（算術平均）
    final midpointTimestamp = (personA.dateTime.millisecondsSinceEpoch +
            personB.dateTime.millisecondsSinceEpoch) ~/
        2;
    final midpointDateTime =
        DateTime.fromMillisecondsSinceEpoch(midpointTimestamp);
    logger.d('時空中點日期時間: $midpointDateTime');

    // 計算兩人出生地點的中點（經緯度平均）
    final midpointLatitude = (personA.latitude + personB.latitude) / 2;
    final midpointLongitude = (personA.longitude + personB.longitude) / 2;
    logger.d('時空中點經緯度: $midpointLatitude, $midpointLongitude');

    // 使用中點時間和地點計算宮位
    final houses = await calculateHouses(
        midpointDateTime, midpointLatitude, midpointLongitude,
        houseSystem: params.chartSettings?.houseSystem);
    logger.d('時空中點盤宮位計算完成');

    // 使用中點時間和地點計算行星位置
    final planets = await calculatePlanetPositions(
      midpointDateTime,
      latitude: midpointLatitude,
      longitude: midpointLongitude,
      housesData: houses,
      planetVisibility: params.planetVisibility,
    );

    _addSpecialPointsIfNeeded(
      positions: planets,
      housesData: houses,
      planetVisibility: params.planetVisibility,
    );

    logger.d('時空中點盤行星位置計算完成');

    // 計算相位
    final aspects = calculateAspects(planets, aspectOrbs: params.aspectOrbs);
    logger.d('時空中點盤相位計算完成');

    // 返回計算結果
    return ChartData(
      chartType: ChartType.davison,
      primaryPerson: personA,
      secondaryPerson: personB,
      planets: planets,
      houses: houses,
      aspects: aspects,
      specificDate: midpointDateTime, // 將中點時間存儲在 specificDate 中，供推運計算使用
    );
  }

  /// 計算時空次限盤數據
  ///
  /// 時空次限盤是先計算兩人出生時間和地點的中點，然後直接將這個中點進行次限推運
  Future<ChartData> _calculateDavisonSecondaryChart(
      ChartCalculationParams params) async {
    logger.i('計算時空次限盤數據');

    // 確保有第二個人的數據
    if (params.chartData.secondaryPerson == null) {
      throw Exception('時空次限盤需要兩個人的數據');
    }

    // 確保有特定日期
    if (params.chartData.specificDate == null) {
      throw Exception('時空次限盤需要指定目標日期');
    }

    final personA = params.chartData.primaryPerson;
    final personB = params.chartData.secondaryPerson!;

    // 1. 計算兩人出生時間的中點（算術平均）
    final midpointTimestamp = (personA.dateTime.millisecondsSinceEpoch +
            personB.dateTime.millisecondsSinceEpoch) ~/
        2;
    final midpointDateTime =
        DateTime.fromMillisecondsSinceEpoch(midpointTimestamp);
    logger.d('時空中點日期時間: $midpointDateTime');

    // 2. 計算兩人出生地點的中點（經緯度平均）
    final midpointLatitude = (personA.latitude + personB.latitude) / 2;
    final midpointLongitude = (personA.longitude + personB.longitude) / 2;
    logger.d('時空中點經緯度: $midpointLatitude, $midpointLongitude');

    // 3. 計算時空中點的次限推運日期
    final secondaryDate = AstrologyCalculator.calculateSecondaryProgressionDate(
      midpointDateTime,
      params.chartData.specificDate!,
    );
    logger.d('時空中點次限推運日期: $secondaryDate');

    // 4. 計算時空次限盤宮位
    HouseCuspData? houseCuspData = await calculateSecondaryProgressedHouses(
        midpointDateTime,
        params.chartData.specificDate!,
        midpointLatitude,
        midpointLongitude,
        params);

    logger.d('時空次限盤宮位計算完成');

    // 5. 計算時空次限盤行星位置
    final planets = await calculatePlanetPositions(
      secondaryDate,
      latitude: midpointLatitude,
      longitude: midpointLongitude,
      housesData: houseCuspData,
      planetVisibility: params.planetVisibility,
    );

    _addSpecialPointsIfNeeded(
      positions: planets,
      housesData: houseCuspData,
      planetVisibility: params.planetVisibility,
    );

    logger.d('時空次限盤行星位置計算完成');

    // 6. 計算相位
    final aspects = calculateAspects(planets, aspectOrbs: params.aspectOrbs);
    logger.d('時空次限盤相位計算完成');

    // 回傳時空次限盤資料
    return ChartData(
      chartType: ChartType.davisonSecondary,
      primaryPerson: personA,
      secondaryPerson: personB,
      planets: planets,
      houses: houseCuspData,
      aspects: aspects,
      specificDate: params.chartData.specificDate,
    );
  }

  /// 計算時空三限盤數據
  ///
  /// 時空三限盤是先計算兩人出生時間和地點的中點，然後直接將這個中點進行三限推運
  Future<ChartData> _calculateDavisonTertiaryChart(
      ChartCalculationParams params) async {
    logger.i('計算時空三限盤數據');

    // 確保有第二個人的數據
    if (params.chartData.secondaryPerson == null) {
      throw Exception('時空三限盤需要兩個人的數據');
    }

    // 確保有特定日期
    if (params.chartData.specificDate == null) {
      throw Exception('時空三限盤需要指定目標日期');
    }

    final personA = params.chartData.primaryPerson;
    final personB = params.chartData.secondaryPerson!;

    // 1. 計算兩人出生時間的中點（算術平均）
    final midpointTimestamp = (personA.dateTime.millisecondsSinceEpoch +
            personB.dateTime.millisecondsSinceEpoch) ~/
        2;
    final midpointDateTime =
        DateTime.fromMillisecondsSinceEpoch(midpointTimestamp);
    logger.d('時空中點日期時間: $midpointDateTime');

    // 2. 計算兩人出生地點的中點（經緯度平均）
    final midpointLatitude = (personA.latitude + personB.latitude) / 2;
    final midpointLongitude = (personA.longitude + personB.longitude) / 2;
    logger.d('時空中點經緯度: $midpointLatitude, $midpointLongitude');

    // 3. 計算時空中點的三限推運日期
    final tertiaryDate = AstrologyCalculator.calculateTertiaryProgressionDate(
      midpointDateTime,
      params.chartData.specificDate!,
    );
    logger.d('時空中點三限推運日期: $tertiaryDate');

    // 4. 計算時空三限盤宮位
    HouseCuspData? houseCuspData = await calculateTertiaryProgressedHouses(
        midpointDateTime,
        params.chartData.specificDate!,
        midpointLatitude,
        midpointLongitude,
        params);
    logger.d('時空三限盤宮位計算完成');

    // 5. 計算時空三限盤行星位置
    final planets = await calculatePlanetPositions(
      tertiaryDate,
      latitude: midpointLatitude,
      longitude: midpointLongitude,
      housesData: houseCuspData,
      planetVisibility: params.planetVisibility,
    );

    _addSpecialPointsIfNeeded(
      positions: planets,
      housesData: houseCuspData,
      planetVisibility: params.planetVisibility,
    );

    logger.d('時空三限盤行星位置計算完成');

    // 6. 計算相位
    final aspects = calculateAspects(planets, aspectOrbs: params.aspectOrbs);
    logger.d('時空三限盤相位計算完成');

    // 回傳時空三限盤資料
    return ChartData(
      chartType: ChartType.davisonTertiary,
      primaryPerson: personA,
      secondaryPerson: personB,
      planets: planets,
      houses: houseCuspData,
      aspects: aspects,
      specificDate: params.chartData.specificDate,
    );
  }

  /// 計算馬克思盤數據
  ///
  /// 馬克思盤是基於兩人本命盤的特殊計算方法，反映關係的深層動力
  Future<ChartData> _calculateMarksChart(ChartCalculationParams params) async {
    logger.i('計算馬克思盤數據');

    final personA = params.chartData.primaryPerson;
    final personB = params.chartData.secondaryPerson;
    if (personB == null) {
      throw Exception('馬克思盤需要兩個人的數據');
    }

    // 計算兩人出生時間的中點（算術平均）
    final midpointTimestamp = (personA.dateTime.millisecondsSinceEpoch +
            personB.dateTime.millisecondsSinceEpoch) ~/
        2;
    final midpointDateTime =
        DateTime.fromMillisecondsSinceEpoch(midpointTimestamp);
    logger.d('兩人出生時間的中點: $midpointDateTime');

    // 計算兩人出生地點的中點（經緯度平均）
    final midpointLatitude = (personA.latitude + personB.latitude) / 2;
    final midpointLongitude = (personA.longitude + personB.longitude) / 2;
    logger.d('兩人出生地點的中點: $midpointLatitude, $midpointLongitude');

    // 計算第一個人的出生時間/地點與中點的中點
    final marksTimestamp =
        (personA.dateTime.millisecondsSinceEpoch + midpointTimestamp) ~/ 2;
    final marksDateTime = DateTime.fromMillisecondsSinceEpoch(marksTimestamp);
    logger.d('馬克思盤時間: $marksDateTime');

    final marksLatitude = (personA.latitude + midpointLatitude) / 2;
    final marksLongitude = (personA.longitude + midpointLongitude) / 2;
    logger.d('馬克思盤地點: $marksLatitude, $marksLongitude');

    // 使用新的時間和地點計算宮位
    final houses = await calculateHouses(
        marksDateTime, marksLatitude, marksLongitude,
        houseSystem: params.chartSettings?.houseSystem);

    // 確保 houses 不為 null
    if (houses == null) {
      logger.w('無法計算馬克思盤：缺少宮位數據');
      // 建立預設的宮位和行星數據
      final defaultHouses = HouseCuspData(
        List<double>.filled(13, 0.0),
        List<double>.filled(10, 0.0),
      );

      // 計算預設行星位置
      final defaultPlanets = await calculatePlanetPositions(
        personA.dateTime,
        latitude: personA.latitude,
        longitude: personA.longitude,
        housesData: defaultHouses,
        planetVisibility: params.planetVisibility,
      );

      _addSpecialPointsIfNeeded(
        positions: defaultPlanets,
        housesData: defaultHouses,
        planetVisibility: params.planetVisibility,
      );

      // 返回預設結果
      return ChartData(
        chartType: ChartType.marks,
        primaryPerson: personA,
        secondaryPerson: personB,
        planets: defaultPlanets,
        houses: defaultHouses,
        aspects: [],
        specificDate: marksDateTime, // 將馬克思盤的計算時間存儲在 specificDate 中
      );
    }

    // 使用新的時間和地點計算行星位置
    final planets = await calculatePlanetPositions(
      marksDateTime,
      latitude: marksLatitude,
      longitude: marksLongitude,
      housesData: houses,
      planetVisibility: params.planetVisibility,
    );

    _addSpecialPointsIfNeeded(
      positions: planets,
      housesData: houses,
      planetVisibility: params.planetVisibility,
    );

    logger.d('馬克思盤行星位置計算完成');

    // 計算相位
    final aspects = calculateAspects(planets, aspectOrbs: params.aspectOrbs);
    logger.d('馬克思盤相位計算完成');

    // 返回計算結果
    return ChartData(
      chartType: ChartType.marks,
      primaryPerson: personA,
      secondaryPerson: personB,
      planets: planets,
      houses: houses,
      aspects: aspects,
      specificDate: marksDateTime, // 將馬克思盤的計算時間存儲在 specificDate 中
    );
  }

  /// 計算馬克思次限盤數據
  ///
  /// 馬克思次限盤是將馬克思盤進行次限推運，觀察關係深層發展
  Future<ChartData> _calculateMarksSecondaryChart(
      ChartCalculationParams params) async {
    logger.i('計算馬克思次限盤數據');

    // 確保有第二個人的數據
    if (params.chartData.secondaryPerson == null) {
      throw Exception('馬克思次限盤需要兩個人的數據');
    }

    // 確保有特定日期
    if (params.chartData.specificDate == null) {
      throw Exception('馬克思次限盤需要指定目標日期');
    }

    // 將馬克思盤的行星位置視為出生時的行星位置
    final personA = params.chartData.primaryPerson;
    final personB = params.chartData.secondaryPerson!;

    // 計算兩人出生時間的中點（算術平均）
    final midpointTimestamp = (personA.dateTime.millisecondsSinceEpoch +
            personB.dateTime.millisecondsSinceEpoch) ~/
        2;

    // 計算第一個人的出生時間與中點的中點
    final marksTimestamp =
        (personA.dateTime.millisecondsSinceEpoch + midpointTimestamp) ~/ 2;
    final marksBirthDate = DateTime.fromMillisecondsSinceEpoch(marksTimestamp);
    logger.d('馬克思盤出生日期: $marksBirthDate');

    // 計算兩人出生地點的中點（經緯度平均）
    final midpointLatitude = (personA.latitude + personB.latitude) / 2;
    final midpointLongitude = (personA.longitude + personB.longitude) / 2;

    // 計算第一個人的出生地點與中點的中點
    final marksLatitude = (personA.latitude + midpointLatitude) / 2;
    final marksLongitude = (personA.longitude + midpointLongitude) / 2;

    // 創建一個虛擬的出生數據來計算次限推運日期
    final marksBirthData = BirthData(
      id: 'marks_chart',
      name: '馬克思盤',
      dateTime: marksBirthDate,
      birthPlace: '馬克思地點',
      latitude: marksLatitude,
      longitude: marksLongitude,
    );

    HouseCuspData? houseCuspData = await calculateSecondaryProgressedHouses(
        marksBirthDate,
        params.chartData.specificDate!,
        marksLatitude,
        marksLongitude,
        params);

    // 計算馬克思盤的次限推運日期
    final secondaryDate = AstrologyCalculator.calculateSecondaryProgressionDate(
      marksBirthData.dateTime,
      params.chartData.specificDate!,
    );
    logger.d('馬克思盤次限推運日期: $secondaryDate');

    // 計算次限推運行星位置
    final secondaryPlanets = await calculatePlanetPositions(
      secondaryDate,
      housesData: houseCuspData,
      latitude: marksLatitude,
      longitude: marksLongitude,
      planetVisibility: params.planetVisibility,
    );

    _addSpecialPointsIfNeeded(
      positions: secondaryPlanets,
      housesData: houseCuspData,
      planetVisibility: params.planetVisibility,
    );

    // 計算相位
    final aspects =
        calculateAspects(secondaryPlanets, aspectOrbs: params.aspectOrbs);

    // 回傳馬克思次限盤資料
    return ChartData(
      chartType: ChartType.marksSecondary,
      primaryPerson: personA,
      secondaryPerson: personB,
      planets: secondaryPlanets,
      houses: houseCuspData,
      aspects: aspects,
      specificDate: params.chartData.specificDate,
    );
  }

  /// 計算馬克思三限盤數據
  ///
  /// 馬克思三限盤是將馬克思盤進行三限推運，觀察關係短期變化
  Future<ChartData> _calculateMarksTertiaryChart(
      ChartCalculationParams params) async {
    logger.i('計算馬克思三限盤數據');

    // 確保有第二個人的數據
    if (params.chartData.secondaryPerson == null) {
      throw Exception('馬克思三限盤需要兩個人的數據');
    }

    // 確保有特定日期
    if (params.chartData.specificDate == null) {
      throw Exception('馬克思三限盤需要指定目標日期');
    }

    // 將馬克思盤的行星位置視為出生時的行星位置
    final personA = params.chartData.primaryPerson;
    final personB = params.chartData.secondaryPerson!;

    // 計算兩人出生時間的中點（算術平均）
    final midpointTimestamp = (personA.dateTime.millisecondsSinceEpoch +
            personB.dateTime.millisecondsSinceEpoch) ~/
        2;

    // 計算第一個人的出生時間與中點的中點
    final marksTimestamp =
        (personA.dateTime.millisecondsSinceEpoch + midpointTimestamp) ~/ 2;
    final marksBirthDate = DateTime.fromMillisecondsSinceEpoch(marksTimestamp);
    logger.d('馬克思盤出生日期: $marksBirthDate');

    // 計算兩人出生地點的中點（經緯度平均）
    final midpointLatitude = (personA.latitude + personB.latitude) / 2;
    final midpointLongitude = (personA.longitude + personB.longitude) / 2;

    // 計算第一個人的出生地點與中點的中點
    final marksLatitude = (personA.latitude + midpointLatitude) / 2;
    final marksLongitude = (personA.longitude + midpointLongitude) / 2;

    // 創建一個虛擬的出生數據來計算三限推運日期
    final marksBirthData = BirthData(
      id: 'marks_chart',
      name: '馬克思盤',
      dateTime: marksBirthDate,
      birthPlace: '馬克思地點',
      latitude: marksLatitude,
      longitude: marksLongitude,
    );

    // 計算馬克思盤的三限推運日期
    final tertiaryDate = AstrologyCalculator.calculateTertiaryProgressionDate(
      marksBirthData.dateTime,
      params.chartData.specificDate!,
    );
    logger.d('馬克思盤三限推運日期: $tertiaryDate');

    HouseCuspData? houseCuspData = await calculateTertiaryProgressedHouses(
        marksBirthDate,
        params.chartData.specificDate!,
        marksLatitude,
        marksLongitude,
        params);

    // 計算三限推運行星位置
    final tertiaryPlanets = await calculatePlanetPositions(
      tertiaryDate,
      housesData: houseCuspData,
      latitude: marksLatitude,
      longitude: marksLongitude,
      planetVisibility: params.planetVisibility,
    );

    _addSpecialPointsIfNeeded(
      positions: tertiaryPlanets,
      housesData: houseCuspData,
      planetVisibility: params.planetVisibility,
    );

    // 計算相位
    final aspects =
        calculateAspects(tertiaryPlanets, aspectOrbs: params.aspectOrbs);

    // 回傳馬克思三限盤資料
    return ChartData(
      chartType: ChartType.marksTertiary,
      primaryPerson: personA,
      secondaryPerson: personB,
      planets: tertiaryPlanets,
      houses: houseCuspData,
      aspects: aspects,
      specificDate: params.chartData.specificDate,
    );
  }

  Future<ChartData> _calculateSynastryChart(
      ChartCalculationParams params) async {
    logger.i('計算比較盤數據');

    // 檢查是否有第二個人的資料
    if (params.chartData.secondaryPerson == null) {
      logger.e('比較盤需要兩個人的數據，但 secondaryPerson 為 null');
      throw Exception('比較盤需要兩個人的數據');
    }

    // 比較盤：使用第二個人的出生時間計算行星位置，但使用第一個人的宮位
    // 計算第一個人的宮位
    final primaryHouses = await calculateHouses(
        params.chartData.primaryPerson.dateTime,
        params.chartData.primaryPerson.latitude,
        params.chartData.primaryPerson.longitude,
        houseSystem: params.chartSettings?.houseSystem);
    params.chartData.houses = primaryHouses;

    // 計算第一個人的行星位置
    final primaryPlanets = await calculatePlanetPositions(
      params.chartData.primaryPerson.dateTime,
      housesData: primaryHouses,
      latitude: params.chartData.primaryPerson.latitude,
      longitude: params.chartData.primaryPerson.longitude,
      planetVisibility: params.planetVisibility,
    );
    params.chartData.primaryPerson.planets = primaryPlanets;

    _addSpecialPointsIfNeeded(
      positions: primaryPlanets,
      housesData: primaryHouses,
      planetVisibility: params.planetVisibility,
    );

    final secondaryHouses = await calculateHouses(
        params.chartData.secondaryPerson!.dateTime,
        params.chartData.secondaryPerson!.latitude,
        params.chartData.secondaryPerson!.longitude,
        houseSystem: params.chartSettings?.houseSystem);

    // 計算第二個人的行星位置，但使用第一個人的宮位
    final secondaryPlanets = await calculatePlanetPositions(
      params.chartData.secondaryPerson!.dateTime,
      housesData: primaryHouses, // 使用第一個人的宮位
      latitude: params.chartData.secondaryPerson!.latitude,
      longitude: params.chartData.secondaryPerson!.longitude,
      planetVisibility: params.planetVisibility,
    );

    _addSpecialPointsIfNeededDual(
      positions: secondaryPlanets,
      housesData: secondaryHouses,
      housesDataDual: primaryHouses!,
      planetVisibility: params.planetVisibility,
    );

    params.chartData.secondaryPerson!.planets = secondaryPlanets;
    params.chartData.planets = secondaryPlanets; // 設置主要行星為第二個人的行星

    params.chartData.primaryPerson.aspects = calculateDualChartAspects(
        primaryPlanets, secondaryPlanets,
        aspectOrbs: params.aspectOrbs);

    params.chartData.secondaryPerson!.aspects = calculateDualChartAspects(
        secondaryPlanets, primaryPlanets,
        aspectOrbs: params.aspectOrbs);

    // 計算相位
    params.chartData.aspects = params.chartData.primaryPerson.aspects;

    return params.chartData;
  }

  /// 計算天象盤數據
  ///
  /// 天象盤顯示特定時間點的星象配置，用於觀察全球或特定地區的整體運勢
  Future<ChartData> _calculateMundaneChart(
      ChartCalculationParams params) async {
    logger.i('計算天象盤數據');

    // 確保有特定日期
    params.chartData.specificDate ??= DateTime.now();

    logger.d('目標日期: ${params.chartData.specificDate}');

    // 計算宮位
    HouseCuspData? houses = await calculateHouses(
        params.chartData.specificDate!,
        params.effectiveLatitude,
        params.effectiveLongitude,
        houseSystem: params.chartSettings?.houseSystem);
    logger.d('天象盤宮位計算完成');

    // 計算行星位置
    params.chartData.primaryPerson.planets = await calculatePlanetPositions(
      params.chartData.specificDate!,
      housesData: houses,
      latitude: params.effectiveLatitude,
      longitude: params.effectiveLongitude,
      planetVisibility: params.planetVisibility,
    );

    _addSpecialPointsIfNeeded(
      positions: params.chartData.primaryPerson.planets!,
      housesData: houses,
      planetVisibility: params.planetVisibility,
    );
    logger.d('天象盤行星位置計算完成');

    // 計算相位
    params.chartData.primaryPerson.aspects = calculateAspects(
        params.chartData.primaryPerson.planets!,
        aspectOrbs: params.aspectOrbs);
    logger.d('天象盤相位計算完成');

    // 返回計算結果
    return ChartData(
      chartType: params.chartData.chartType,
      primaryPerson: params.chartData.primaryPerson,
      specificDate: params.chartData.specificDate,
      planets: params.chartData.primaryPerson.planets!,
      houses: houses,
      aspects: params.chartData.primaryPerson.aspects,
    );
  }

  Future<BirthData> generateSecondaryProgressedChart({
    required DateTime targetDate,
    required BirthData person,
    required ChartCalculationParams params,
    required ChartType chartType,
  }) async {
    logger.i('🪞 開始計算次限推運盤');

    logger.d('出生日期: ${person.dateTime}');
    logger.d('目標日期: $targetDate');

    // 計算次限推運日期（象徵出生後某日）
    final secondaryDate = AstrologyCalculator.calculateSecondaryProgressionDate(
      person.dateTime,
      targetDate,
    );
    logger.d('次限推運對應出生後某日: $secondaryDate');

    // 計算推進的天數與推運日期（用來計算宮位）
    final progressedDays =
        AstrologyCalculator.calculateSecondaryProgressionYears(
      person.dateTime,
      targetDate,
    );
    final progressedDate = person.dateTime.add(Duration(days: progressedDays));
    logger.d('推運後宮位計算使用日期: $progressedDate');

    // 計算宮位
    final houses = await calculateHouses(
        progressedDate, person.latitude, person.longitude,
        houseSystem: params.chartSettings?.houseSystem);
    logger.d('次限推運宮位計算完成');

    if (houses != null) {
      logger.d('↑ 上升點經度: ${houses.ascmc[0]}');
      logger.d('☊ 中天經度: ${houses.ascmc[1]}');
    }

    // 計算行星位置（使用象徵日期）
    final planets = await calculatePlanetPositions(
      secondaryDate,
      housesData: houses,
      latitude: person.latitude,
      longitude: person.longitude,
      planetVisibility: params.planetVisibility,
    );

    _addSpecialPointsIfNeeded(
      positions: planets,
      housesData: houses,
      planetVisibility: params.planetVisibility,
    );

    logger.d('行星位置計算完成，共 ${planets.length} 顆天體');

    // 使用獨立方法處理阿拉伯點計算
    final arabicPoints = await processArabicPoints(
      calculateArabicPoints: params.calculateArabicPoints,
      birthDate: secondaryDate,
      planets: planets,
      houses: houses,
      latitude: person.latitude,
      longitude: person.longitude,
      planetVisibility: params.planetVisibility,
    );

    // 計算相位
    final aspects = calculateAspects(planets, aspectOrbs: params.aspectOrbs);

    logger.i('次限推運盤完成');
    person.planets = planets;
    person.houses = houses;
    person.aspects = aspects;

    return person;
  }

  Future<BirthData> generateTertiaryProgressedChart({
    required DateTime birthDate,
    required DateTime targetDate,
    required BirthData person,
    required double latitude,
    required double longitude,
    required ChartCalculationParams params,
    required ChartType chartType,
  }) async {
    logger.i('🪞 開始計算三限推運盤');

    logger.d('出生日期: $birthDate');
    logger.d('目標日期: $targetDate');

    // 計算三限推運日期（象徵出生後某月）
    final tertiaryDate = AstrologyCalculator.calculateTertiaryProgressionDate(
      birthDate,
      targetDate,
    );
    logger.d('三限推運對應出生後某日: $tertiaryDate');

    // 計算宮位
    final houses = await calculateHouses(tertiaryDate, latitude, longitude,
        houseSystem: params.chartSettings?.houseSystem);
    logger.d('三限推運宮位計算完成');

    if (houses != null) {
      logger.d('↑ 上升點經度: ${houses.ascmc[0]}');
      logger.d('☊ 中天經度: ${houses.ascmc[1]}');
    }

    // 計算行星位置（使用象徵日期）
    final planets = await calculatePlanetPositions(
      tertiaryDate,
      housesData: houses,
      latitude: latitude,
      longitude: longitude,
      planetVisibility: params.planetVisibility,
    );

    _addSpecialPointsIfNeeded(
      positions: planets,
      housesData: houses,
      planetVisibility: params.planetVisibility,
    );

    logger.d('行星位置計算完成，共 ${planets.length} 顆天體');

    // 使用獨立方法處理阿拉伯點計算
    final arabicPoints = await processArabicPoints(
      calculateArabicPoints: params.calculateArabicPoints,
      birthDate: tertiaryDate,
      planets: planets,
      houses: houses,
      latitude: person.latitude,
      longitude: person.longitude,
      planetVisibility: params.planetVisibility,
    );

    // 計算相位
    final aspects = calculateAspects(planets, aspectOrbs: params.aspectOrbs);
    logger.d('🔗 相位數量：${aspects.length}');

    logger.i('✅ 三限推運盤完成');
    person.planets = planets;
    person.houses = houses;
    person.aspects = aspects;

    return person;
  }

  /// 計算次限推運盤數據
  ///
  /// 次限推運盤是一種預測性星盤，一天代表一年，用於分析內在心理成長
  Future<ChartData> _calculateSecondaryProgressionChart(
      ChartCalculationParams params) async {
    logger.i('計算次限推運盤數據');

    // 確保有特定日期
    if (params.chartData.specificDate == null) {
      throw Exception('次限推運盤需要指定目標日期');
    }

    logger.d('出生日期: ${params.chartData.primaryPerson.dateTime}');
    logger.d('目標日期: ${params.chartData.specificDate}');

    // 計算次限推運日期
    final secondaryDate = AstrologyCalculator.calculateSecondaryProgressionDate(
      params.chartData.primaryPerson.dateTime,
      params.chartData.specificDate!,
    );
    logger.d('次限推運日期: $secondaryDate');

    // 計算推進天數
    final progressedDays =
        AstrologyCalculator.calculateSecondaryProgressionYears(
      params.chartData.primaryPerson.dateTime,
      params.chartData.specificDate!,
    );

    // 計算推進日期
    final progressedDate = params.chartData.primaryPerson.dateTime.add(Duration(
      days: progressedDays,
    ));

    // 計算宮位
    HouseCuspData? houses = await calculateHouses(
        progressedDate, params.effectiveLatitude, params.effectiveLongitude,
        houseSystem: params.chartSettings?.houseSystem);
    logger.d('次限推運宮位計算完成');

    if (houses != null) {
      logger.d('上升點經度: ${houses.ascmc[0]}');
      logger.d('中天經度: ${houses.ascmc[1]}');
    }

    // 計算行星位置
    logger.d('開始計算次限推運行星位置');
    final planets = await calculatePlanetPositions(
      secondaryDate,
      housesData: houses,
      latitude: params.effectiveLatitude,
      longitude: params.effectiveLongitude,
      planetVisibility: params.planetVisibility,
    );

    _addSpecialPointsIfNeeded(
      positions: planets,
      housesData: houses,
      planetVisibility: params.planetVisibility,
    );

    logger.d('次限推運行星位置計算完成');

    // 記錄太陽和月亮的位置，用於調試
    for (final planet in planets) {
      if (planet.name == '太陽') {
        logger.d(
            '太陽位置: 經度=${planet.longitude.toStringAsFixed(2)}, 星座=${planet.sign}, 宮位=${planet.house}');
      } else if (planet.name == '月亮') {
        logger.d(
            '月亮位置: 經度=${planet.longitude.toStringAsFixed(2)}, 星座=${planet.sign}, 宮位=${planet.house}');
      }
    }

    // 計算相位
    logger.d('開始計算次限推運相位');
    final aspects = calculateAspects(planets, aspectOrbs: params.aspectOrbs);
    logger.d('次限推運相位計算完成');
    logger.d('相位數量: ${aspects.length}');

    // 返回計算結果
    return ChartData(
      chartType: params.chartData.chartType,
      primaryPerson: params.chartData.primaryPerson,
      specificDate: params.chartData.specificDate,
      planets: planets,
      houses: houses,
      aspects: aspects,
    );
  }

  /// 根據星盤類型計算星盤數據
  Future<ChartData> calculateChartData(
    ChartData chartData, {
    double? latitude,
    double? longitude,
    HouseCuspData? housesData,
    bool calculateArabicPoints = true, // 是否計算阿拉伯點
    ChartSettings? chartSettings,
  }) async {
    // 創建計算參數對象
    final params = ChartCalculationParams(
      chartData: chartData,
      latitude: latitude,
      longitude: longitude,
      housesData: housesData,
      calculateArabicPoints: calculateArabicPoints,
      chartSettings: chartSettings,
      planetVisibility: chartSettings?.planetVisibility,
      aspectOrbs: chartSettings?.aspectOrbs,
    );
    ChartData chartDataResult;
    // 根據星盤類型調用相應的計算方法
    switch (chartData.chartType) {
      case ChartType.natal:
        // 本命盤：顯示一個人出生時的行星位置
        chartDataResult = await _calculateNatalChart(params);

      case ChartType.transit:
        // 行運盤：計算當前天空的行星位置與本命盤對比
        chartDataResult = await _calculateTransitChart(params);

      case ChartType.secondaryProgression:
        // 次限推運盤：一天代表一年，分析內在心理成長
        chartDataResult = await _calculateSecondaryProgressionChart(params);

      case ChartType.tertiaryProgression:
        // 三限推運盤：一天代表一個月，反映短期心理變化
        chartDataResult = await _calculateTertiaryProgressionChart(params);

      case ChartType.solarArcDirection:
        // 太陽弧推運盤：所有行星以太陽推進度數前進，觀察重大人生變化
        chartDataResult = await _calculateSolarArcDirectionChart(params);

      case ChartType.mundane:
        // 天象盤：顯示特定時間點的星象配置，用於觀察全球或特定地區的整體運勢
        return await _calculateMundaneChart(params);

      case ChartType.solarReturn:
        // 太陽返照盤：太陽回到出生位置時的星盤，用於年度運勢分析
        return await _calculateSolarReturnChart(params);

      case ChartType.lunarReturn:
        // 月亮返照盤：月亮回到出生位置時的星盤，用於月度運勢分析
        chartDataResult = await _calculateLunarReturnChart(params);

      case ChartType.synastry:
        // 比較盤：使用第二個人的出生時間計算行星位置，但使用第一個人的宮位
        chartDataResult = await _calculateSynastryChart(params);
      case ChartType.davison:
        // 時空中點盤：以兩人生日取算術平均，推算新時間與地點建立星盤
        chartDataResult = await _calculateDavisonChart(params);
      case ChartType.marks:
        // 馬克思盤：基於兩人本命盤的特殊計算方法，反映關係的深層動力
        chartDataResult = await _calculateMarksChart(params);
      case ChartType.composite:
        // 組合盤：將兩人星盤取中點，產生新的關係盤
        chartDataResult = await _calculateCompositeChart(params);

      case ChartType.synastrySecondary:
        // 比較次限盤：使用第二個人的次限推運盤計算行星位置，但使用第一個人的宮位
        chartDataResult = await _calculateSynastrySecondaryChart(params);
      case ChartType.synastryTertiary:
        // 比較三限盤：使用第二個人的三限推運盤計算行星位置，但使用第一個人的宮位
        chartDataResult = await _calculateSynastryTertiaryChart(params);

      case ChartType.compositeSecondary:
        // 組合次限盤：將組合盤進行次限推運，觀察關係長期發展
        chartDataResult = await _calculateCompositeSecondaryChart(params);
      case ChartType.compositeTertiary:
        // 組合三限盤：將組合盤進行三限推運，觀察關係短期變化
        chartDataResult = await _calculateCompositeTertiaryChart(params);

      case ChartType.davisonSecondary:
        // 時空次限盤：將時空中點盤進行次限推運，觀察關係長期發展
        chartDataResult = await _calculateDavisonSecondaryChart(params);
      case ChartType.davisonTertiary:
        // 時空三限盤：將時空中點盤進行三限推運，觀察關係短期變化
        chartDataResult = await _calculateDavisonTertiaryChart(params);

      case ChartType.marksSecondary:
        // 馬克思次限盤：將馬克思盤進行次限推運，觀察關係深層發展
        chartDataResult = await _calculateMarksSecondaryChart(params);
      case ChartType.marksTertiary:
        // 馬克思三限盤：將馬克思盤進行三限推運，觀察關係短期變化
        chartDataResult = await _calculateMarksTertiaryChart(params);

      // 時刻盤（占星卜卦）
      case ChartType.horary:
      case ChartType.event:
        // 時刻盤：使用提問時間計算行星位置
        chartDataResult = await _calculateNatalChart(params);

      // 法達盤
      case ChartType.firdaria:
        // 法達盤使用本命盤的計算方法，因為法達盤數據會在專門的頁面中計算
        chartDataResult = await _calculateFirdariaChart(params);

      // 小限法
      case ChartType.profection:
        // 小限法使用本命盤的計算方法，因為小限法數據會在專門的頁面中計算
        chartDataResult = await _calculateProfectionChart(params);

      // 二分二至圖
      case ChartType.equinoxSolstice:
        // 二分二至圖：顯示春分、夏至、秋分、冬至四個節氣的星盤配置與個人影響
        chartDataResult = await _calculateEquinoxSolsticeChart(params);
      case ChartType.eclipse:
        chartDataResult = await _calculateNatalChart(params);
      case ChartType.conjunctionJupiterSaturn:
        chartDataResult = await _calculateNatalChart(params);
      case ChartType.conjunctionMarsSaturn:
        chartDataResult = await _calculateNatalChart(params);
    }

    if (chartDataResult.primaryPerson.isTimeUncertain) {
      chartDataResult.planets =
          chartDataResult.planets?.where((p) => p.id <= AstrologyConstants.PLUTO).toList() ?? [];
      chartDataResult.planets?.removeWhere((p) => p.id == AstrologyConstants.MOON);
      chartDataResult.arabicPoints = null;
    }
    return chartDataResult;
  }

  /// 計算二分二至圖數據
  ///
  /// 二分二至圖顯示春分、夏至、秋分、冬至四個節氣的星盤配置
  Future<ChartData> _calculateEquinoxSolsticeChart(
      ChartCalculationParams params) async {
    logger.i('計算二分二至圖數據');

    // 確保有特定日期，如果沒有則使用當前時間
    final DateTime targetDate = params.chartData.specificDate ?? DateTime.now();

    logger.d('目標日期: $targetDate');

    // 計算宮位
    HouseCuspData? houses = await calculateHouses(
        targetDate, params.effectiveLatitude, params.effectiveLongitude,
        houseSystem: params.chartSettings?.houseSystem);
    logger.d('二分二至圖宮位計算完成');

    // 計算行星位置
    final planets = await calculatePlanetPositions(
      targetDate,
      housesData: houses,
      latitude: params.effectiveLatitude,
      longitude: params.effectiveLongitude,
      planetVisibility: params.planetVisibility,
    );

    _addSpecialPointsIfNeeded(
      positions: planets,
      housesData: houses,
      planetVisibility: params.planetVisibility,
    );

    logger.d('二分二至圖行星位置計算完成');

    // 計算相位
    final aspects = calculateAspects(planets, aspectOrbs: params.aspectOrbs);
    logger.d('二分二至圖相位計算完成');

    // 使用獨立方法處理阿拉伯點計算
    final arabicPoints = await processArabicPoints(
      calculateArabicPoints: params.calculateArabicPoints,
      birthDate: targetDate,
      planets: planets,
      houses: houses,
      latitude: params.effectiveLatitude,
      longitude: params.effectiveLongitude,
      planetVisibility: params.planetVisibility,
    );
    logger.d('二分二至圖阿拉伯點計算完成');

    // 計算互容接納
    final receptions = calculateReceptions(planets);
    logger.d('二分二至圖互容接納計算完成');

    // 返回結果
    final result = params.chartData.copyWith(
      planets: planets,
      houses: houses,
      aspects: aspects,
      arabicPoints: arabicPoints,
      receptions: receptions,
    );

    logger.i('二分二至圖計算完成');
    return result;
  }
}
