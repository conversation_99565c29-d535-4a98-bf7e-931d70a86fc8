# 返照盤比較模式功能實現

## 📋 功能概述

成功實現了在太陽返照盤和月亮返照盤頁面中新增比較模式切換按鈕，讓用戶可以在「單獨顯示返照盤」和「返照盤與本命盤比較」兩種模式之間切換。

## ✨ 核心功能

### 🎯 **返照盤比較模式系統**

#### **支援的星盤類型**
- 🌞 **太陽返照盤** (`ChartType.solarReturn`) - 可切換為太陽返照盤與本命盤比較
- 🌙 **月亮返照盤** (`ChartType.lunarReturn`) - 可切換為月亮返照盤與本命盤比較

#### **功能特點**
- **智能按鈕顯示**：只在返照盤類型時顯示比較按鈕
- **動態按鈕文字**：根據當前模式和星盤類型顯示適當的按鈕文字
- **視覺狀態指示**：比較模式時按鈕呈現藍色高亮狀態
- **雙圈星盤繪製**：比較模式下使用雙圈繪製器顯示返照盤與本命盤

## 🔧 **技術實現**

### **新增的核心功能**

#### **1. ChartViewModel 狀態管理**
```dart
// 返照盤比較模式狀態
bool _isReturnChartComparisonMode = false;

// 返回返照盤比較模式狀態
bool get isReturnChartComparisonMode => _isReturnChartComparisonMode;

// 設置返照盤比較模式
void setReturnChartComparisonMode(bool value) {
  if (_isReturnChartComparisonMode != value) {
    _isReturnChartComparisonMode = value;
    logger.i('返照盤比較模式切換為: $value');
    notifyListeners();
  }
}
```

#### **2. ChartViewWidget UI 組件**
```dart
// 只在返照盤類型時顯示比較按鈕
if (widget.viewModel.chartType.isReturnChart) ...[
  const SizedBox(width: 10),
  _buildReturnChartComparisonButton(),
],
```

#### **3. 智能按鈕實現**
- **動態文字**：根據星盤類型和當前模式顯示不同文字
  - 太陽返照盤：「與本命比較」↔「太陽返照」
  - 月亮返照盤：「與本命比較」↔「月亮返照」
- **視覺狀態**：比較模式時使用藍色主題色
- **圖標切換**：`Icons.compare` ↔ `Icons.compare_arrows`

#### **4. 雙圈繪製器整合**
```dart
// 返照盤比較模式判斷
final isReturnChartComparison = widget.viewModel.isReturnChartComparisonMode &&
    (chartType == ChartType.solarReturn || chartType == ChartType.lunarReturn);

// 返照盤比較模式：返照盤與本命盤比較
_cachedPainter = DualChartPainter(
  widget.viewModel.chartData.primaryPerson.planets!, // 本命盤行星
  widget.viewModel.chartData.planets!, // 返照盤行星
  widget.viewModel.chartData.aspects!,
  housesData: widget.viewModel.chartData.houses!,
  chartType: chartType,
  chartSettings: widget.viewModel.settingsViewModel?.chartSettings,
  multiChartSettings: widget.viewModel.settingsViewModel?.multiChartSettings,
);
```

## 📱 **用戶體驗**

### **操作流程**
1. 用戶進入太陽返照盤或月亮返照盤頁面
2. 在左側功能按鈕組中看到「與本命比較」按鈕
3. 點擊按鈕切換到比較模式
4. 星盤自動切換為雙圈顯示，內圈為本命盤，外圈為返照盤
5. 按鈕文字變為「太陽返照」或「月亮返照」，顏色變為藍色
6. 再次點擊可切換回單獨顯示模式

### **視覺設計**
- **按鈕位置**：位於界主星和小限法按鈕右側
- **按鈕樣式**：圓角矩形，與其他功能按鈕保持一致
- **狀態指示**：
  - 普通模式：白色背景，灰色邊框和文字
  - 比較模式：淺藍色背景，藍色邊框和文字

## 🎨 **設計理念**

### **符合用戶偏好**
- **無漸層設計**：按鈕使用純色背景，符合用戶偏好的簡潔風格
- **一致性**：與現有功能按鈕保持相同的設計語言
- **直觀性**：按鈕文字和圖標清楚表達當前狀態和功能

### **MVVM 架構**
- **分離關注點**：UI 邏輯與業務邏輯分離
- **狀態管理**：使用 ChangeNotifier 進行狀態通知
- **響應式更新**：狀態變更時自動更新 UI

## 📝 **技術細節**

### **修改的文件**
1. `lib/presentation/viewmodels/chart_viewmodel.dart` - 新增狀態管理
2. `lib/presentation/widgets/chart/chart_view_widget.dart` - 新增 UI 組件和邏輯
3. `lib/data/services/api/astrology_service.dart` - 改進返照盤計算邏輯
4. `lib/shared/widgets/zoomable_chart_widget.dart` - 支援返照盤比較模式的雙圈繪製

### **新增的方法**
- `setReturnChartComparisonMode(bool value)` - 設置比較模式狀態
- `_buildReturnChartComparisonButton()` - 構建比較按鈕 UI
- `_toggleReturnChartComparisonMode()` - 切換比較模式
- 修改 `_needsDualChartPainter()` - 支援返照盤比較模式
- 修改 `_getChartPainter()` - 處理返照盤比較繪製

### **狀態管理**
- 新增 `_isReturnChartComparisonMode` 布爾變數
- 使用 `notifyListeners()` 通知 UI 更新
- 切換模式時清除繪製器緩存，強制重新繪製

### **返照盤計算邏輯改進**
為了支援比較模式，我們改進了太陽返照盤和月亮返照盤的計算邏輯：

#### **太陽返照盤計算改進**
```dart
// 首先計算本命盤行星位置（用於比較模式）
final natalPlanets = await calculatePlanetPositions(
  params.chartData.primaryPerson.dateTime,
  housesData: houses, // 使用返照盤的宮位
  latitude: params.effectiveLatitude,
  longitude: params.effectiveLongitude,
  planetVisibility: params.planetVisibility,
);

// 將本命盤行星數據保存到 primaryPerson 中
params.chartData.primaryPerson.planets = natalPlanets;
```

#### **月亮返照盤計算改進**
同樣的邏輯也應用到月亮返照盤計算中，確保本命盤數據可用於比較模式。

#### **數據結構優化**
- **本命盤數據**：保存在 `chartData.primaryPerson.planets` 中
- **返照盤數據**：保存在 `chartData.planets` 中
- **比較模式**：DualChartPainter 使用兩組數據進行雙圈繪製

### **ZoomableChartWidget 改進**
為了支援返照盤比較模式，我們也更新了 ZoomableChartWidget：

#### **雙圈繪製器判斷邏輯**
```dart
bool _needsDualChartPainter() {
  final chartType = widget.viewModel.chartType;
  final progressionTypes = [ChartType.solarArcDirection];
  final specialDualTypes = [ChartType.transit, ChartType.synastry];

  // 返照盤比較模式
  final isReturnChartComparison = widget.viewModel.isReturnChartComparisonMode &&
      (chartType == ChartType.solarReturn || chartType == ChartType.lunarReturn);

  return progressionTypes.contains(chartType) ||
      specialDualTypes.contains(chartType) ||
      chartType.isSynastryProgression ||
      isReturnChartComparison;
}
```

#### **狀態變化監聽**
```dart
@override
void didUpdateWidget(ZoomableChartWidget oldWidget) {
  super.didUpdateWidget(oldWidget);
  if (oldWidget.viewModel.chartType != widget.viewModel.chartType ||
      oldWidget.viewModel.chartData != widget.viewModel.chartData ||
      oldWidget.viewModel.isReturnChartComparisonMode != widget.viewModel.isReturnChartComparisonMode) {
    _clearCache();
  }
}
```

這確保了當返照盤比較模式狀態變化時，繪製器緩存會被清除，星盤會重新繪製。

## 🔮 **未來擴展**

### **可能的改進**
1. **記憶用戶偏好**：記住用戶上次選擇的模式
2. **快捷鍵支援**：添加鍵盤快捷鍵切換模式
3. **動畫效果**：添加模式切換時的過渡動畫
4. **更多返照盤類型**：支援其他類型的返照盤比較

### **技術優化**
1. **性能優化**：優化雙圈繪製的性能
2. **緩存機制**：改進繪製器緩存策略
3. **錯誤處理**：添加更完善的錯誤處理機制

## ✅ **測試建議**

### **功能測試**
1. 測試太陽返照盤的比較模式切換
2. 測試月亮返照盤的比較模式切換
3. 測試按鈕文字和圖標的正確顯示
4. 測試雙圈星盤的正確繪製
5. 測試在非返照盤類型時按鈕不顯示

### **UI 測試**
1. 測試按鈕的視覺狀態變化
2. 測試按鈕在不同螢幕尺寸下的顯示
3. 測試與其他功能按鈕的對齊和間距

### **數據測試**
1. 測試本命盤數據是否正確保存到 `primaryPerson.planets`
2. 測試返照盤數據是否正確保存到 `chartData.planets`
3. 測試比較模式下雙圈繪製的數據正確性

## 📊 **實現狀態**

- ✅ ChartViewModel 狀態管理
- ✅ 返照盤比較按鈕 UI
- ✅ 雙圈繪製器整合
- ✅ 動態按鈕文字和狀態
- ✅ 智能按鈕顯示邏輯
- ✅ 模式切換功能
- ✅ 返照盤計算邏輯改進
- ✅ 本命盤數據保存機制
- ✅ ZoomableChartWidget 雙圈繪製支援
- ✅ 狀態變化監聽機制
- ✅ 繪製器緩存管理
- ✅ 編譯測試通過
- ✅ Web 構建測試通過
- ✅ 文檔記錄

## 🎊 **功能完成總結**

### **實現的核心功能**
1. **智能按鈕顯示**：只在太陽返照盤和月亮返照盤頁面顯示比較按鈕
2. **動態模式切換**：用戶可以在單獨顯示和比較模式之間自由切換
3. **視覺狀態反饋**：按鈕顏色和文字根據當前模式動態變化
4. **雙圈星盤繪製**：比較模式下正確顯示返照盤與本命盤的雙圈對比
5. **數據結構優化**：改進返照盤計算邏輯，確保本命盤數據可用於比較

### **技術亮點**
- **MVVM 架構**：遵循應用的架構模式，狀態管理清晰
- **無漸層設計**：符合用戶偏好的簡潔 UI 風格
- **智能邏輯**：只在需要時顯示功能，避免界面混亂
- **數據完整性**：確保比較模式下有完整的本命盤和返照盤數據
- **性能優化**：使用繪製器緩存機制，提升渲染效率

**功能已完全實現並通過編譯測試，可投入使用！** 🎉
